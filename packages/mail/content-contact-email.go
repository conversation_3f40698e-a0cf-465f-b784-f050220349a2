package mail

import (
	"context"
	"log"
	"strings"

	"github.com/nsp-inc/vtuber/packages/env"
	"github.com/nsp-inc/vtuber/packages/web"
)

func ContentContactEmail(ctx context.Context, slug string, isCampaign bool, contentName string, name string, email string, projectName string, inquiryType string, inquiryDetails string, sendEmail string) {
	var content strings.Builder
	var subject string
	var url string

	lan := web.GetLanguage(ctx)
	switch lan {

	case "en-us":

		if !isCampaign {
			subject = "Event Inquiry for campaign " + contentName
			url = "https://v-sai.com/event/" + slug

		} else {
			subject = "Campaign Inquiry for campaign " + contentName
			url = "https://v-sai.com/campaign/" + slug

		}

		content.WriteString("Name: " + name + "\n")
		content.WriteString("Email: " + email + "\n")
		content.WriteString("Project Name: " + projectName + "\n")
		content.WriteString("Inquiry Type: " + inquiryType + "\n")
		content.WriteString("Inquiry Details: " + inquiryDetails + "\n")
		content.WriteString("Content URL: " + url + "\n")
		content.WriteString("------------------------------------------------" + "\n")
		content.WriteString("------------------------------------------------" + "\n")
		content.WriteString("If you have any questions, please contact the email address below.\n")
		content.WriteString("Email address: " + contactEmail + "\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("Contact: V Festival Management Staff\n")
		content.WriteString("©2025 デジタルギア株式会社")
	default:
		if !isCampaign {
			subject = "イベントに関するお問い合わせ " + contentName
			url = "https://v-sai.com/event/" + slug

		} else {
			subject = "キャンペーンに関するお問い合わせ " + contentName
			url = "https://v-sai.com/campaign/" + slug

		}
		content.WriteString("お名前: " + name + "\n")
		content.WriteString("メール: " + email + "\n")
		content.WriteString("プロジェクト名: " + projectName + "\n")
		content.WriteString("お問い合わせ種類: " + inquiryType + "\n")
		content.WriteString("お問い合わせ内容: " + inquiryDetails + "\n")
		content.WriteString("コンテンツURL: " + url + "\n")

		content.WriteString("------------------------------------------------" + "\n")
		content.WriteString("ご不明な点がございましたら、下記のメールアドレスにご連絡ください\n")
		content.WriteString("メールアドレス： " + contactEmail + "\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("担当者: V祭運営スタッフ\n")
		content.WriteString("©2025 デジタルギア株式会社")

	}

	toSendEmail := []string{sendEmail}
	mail := Mail{
		To:      toSendEmail,
		Subject: subject,
		Body:    []byte(content.String()),
	}

	err := SendMail(mail)
	if err != nil {
		log.Println("Error sending campaign contact us email:", err)
	}
}

func AdminContactEmail(ctx context.Context, phoneNumber *string, companyName string, name string, email string, projectUrl string, inquiryType string, inquiryDetails string) {
	var content strings.Builder
	var subject string

	lan := web.GetLanguage(ctx)
	switch lan {

	case "en-us":
		subject = "Inquiry From " + name
		content.WriteString("Name: " + name + "\n")
		content.WriteString("Email: " + email + "\n")
		content.WriteString("Company Name: " + companyName + "\n")
		if phoneNumber != nil {
			content.WriteString("Phone Number: " + *phoneNumber + "\n")

		}
		content.WriteString("Project Name: " + projectUrl + "\n")
		content.WriteString("Inquiry Type: " + inquiryType + "\n")
		content.WriteString("Inquiry Details: " + inquiryDetails + "\n")
		content.WriteString("------------------------------------------------" + "\n")
		content.WriteString("------------------------------------------------" + "\n")
		content.WriteString("If you have any questions, please contact the email address below.\n")
		content.WriteString("Email address: " + contactEmail + "\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("Contact: V Festival Management Staff\n")
		content.WriteString("©2025 デジタルギア株式会社")
	default:
		subject = "お問い合わせ先 " + name
		content.WriteString("お名前: " + name + "\n")
		content.WriteString("メール: " + email + "\n")
		content.WriteString("会社名: " + companyName + "\n")
		if phoneNumber != nil {
			content.WriteString("電話番号: " + *phoneNumber + "\n")
		}
		content.WriteString("プロジェクト名: " + projectUrl + "\n")
		content.WriteString("お問い合わせ種類: " + inquiryType + "\n")
		content.WriteString("お問い合わせ内容: " + inquiryDetails + "\n")
		content.WriteString("------------------------------------------------" + "\n")
		content.WriteString("ご不明な点がございましたら、下記のメールアドレスにご連絡ください\n")
		content.WriteString("メールアドレス： " + contactEmail + "\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("担当者: V祭運営スタッフ\n")
		content.WriteString("©2025 デジタルギア株式会社")

	}

	toSendEmail := []string{env.GetString("CONTACT_EMAIL", "")}
	mail := Mail{
		To:      toSendEmail,
		Subject: subject,
		Body:    []byte(content.String()),
	}

	err := SendMail(mail)
	if err != nil {
		log.Println("Error sending email to admin:", err)
	}
}
