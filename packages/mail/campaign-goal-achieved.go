package mail

import (
	"context"
	"log"
	"strings"

	"github.com/nsp-inc/vtuber/packages/web"
)

func CampaignGoalAchievedEmail(ctx context.Context, email []string, campaingnName string, slug string) {
	var content strings.Builder
	var subject string
	var campaignUrl = "https://v-sai.com/campaign/" + slug
	lan := web.GetLanguage(ctx)
	switch lan {
	case "en-us":
		subject = " [V-Sai] The campaign you supported has reached its goal!"
		content.WriteString("Thank you very much for using V-Sai.\n")
		content.WriteString("The campaign you supported reached its goal and was a great success!.\n")
		content.WriteString("Campaign Name:\n")
		content.WriteString(campaingnName + "\n")
		content.WriteString("Campaign Url:\n")
		content.WriteString(campaignUrl + "\n")
		content.WriteString("We will be providing activity reports and information about rewards in the future. \n")
		content.WriteString("Please look forward to it.\n")
		content.WriteString("If you have any questions, please contact the email address below.\n")
		content.WriteString("Email address: " + contactEmail + "\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("Contact: V Festival Management Staff\n")
		content.WriteString("©2025 デジタルギア株式会社")
	default:
		subject = "【V祭】あなたが支援したキャンペーンが目標達成しました！"
		content.WriteString("このたびはV祭をご利用いただき、誠にありがとうございます。\n")
		content.WriteString("あなたにご支援いただいたキャンペーンが、目標金額を達成し、無事に成功いたしました！.\n")
		content.WriteString("キャンペーン名:\n")
		content.WriteString(campaingnName + "\n")
		content.WriteString("キャンペーンURL:\n")
		content.WriteString(campaignUrl + "\n")
		content.WriteString("今後は、活動報告やリターンのご案内をお届けしてまいります。ぜひ楽しみにお待ちください。\n")
		content.WriteString("ぜひ楽しみにお待ちください。.\n")
		content.WriteString("ご不明な点がございましたら、下記のメールアドレスにご連絡ください\n")
		content.WriteString("メールアドレス： " + contactEmail + "\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("担当者: V祭運営スタッフ\n")
		content.WriteString("©2025 デジタルギア株式会社")
	}

	mail := Mail{
		To:      email,
		Subject: subject,
		Body:    []byte(content.String()),
	}

	err := SendMail(mail)
	if err != nil {
		log.Println("Error sending campaign goal achieved email:", err)
	}
}
