syntax = "proto3";

package api.cms.v1;

import "authz/v1/authz.proto";
import "shared/v1/generic.proto";

option go_package = "github.com/nsp-inc/vtuber/api/cms/v1;cmsv1";

message ContactRequest {
  string name = 1; // @gotag: validate:"required"
  string projectName = 2; // @gotag: validate:"required"
  string inquiryType = 3; // @gotag: validate:"required"
  string email = 4; // @gotag: validate:"required"
  string inquiryDetails = 5; // @gotag: validate:"required"
  string id = 6; // @gotag: validate:"required"
}

message AdminContactRequest {
  string companyName = 1; // @gotag: validate:"required"
  string name = 2; // @gotag: validate:"required"
  string projectUrl = 3; // @gotag: validate:"required"
  string inquiryType = 4; // @gotag: validate:"required"
  string email = 5; // @gotag: validate:"required"
  optional string phoneNo = 6;
  string inquiryDetails = 7; // @gotag: validate:"required"
}

service ContactService {
  rpc SendCampaignContactEmail(ContactRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc SendEventContactEmail(ContactRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc SendAdminContactEmail(AdminContactRequest) returns (api.shared.v1.GenericResponse) {}
}
