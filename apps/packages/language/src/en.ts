import { <PERSON><PERSON><PERSON> } from "./types";

export const engish_dictionary: Record<DictionaryKey, string> = {
  already_have_an_account: "If you already have a account, click here",
  login: "Log In",
  my_points: "My points",
  site_search: "Site Search",
  Promotional_Message: "Promotional Message",
  choose_return_plan: "Choose your return plan",
  Benefits: "Benefits",
  Banned_User: "Banned User",
  Banned: "Banned",
  subscription_progress: "Subscription Progress",
  maximum: "Maximum",
  contact_project_owner: "Contact the project owner",
  cancellation_procedure: "Cancellation Procedure",
  Open_for_Subscriptions: "Open for Subscriptions",
  No_Profile: "This user doesn't have a VTuber profile",
  trouble_searching: "Having trouble searching?",
  inquiry_type_placeholder: "- Please select the type of inquiry",
  contact_thanks_page_title: "Thank you for your inquiry.",
  project_contact_thankyou_description_1:
    "We have received your inquiry from the project owner. We will review your inquiry and respond to you by email. The details will be sent to the email address you entered for confirmation.",
  project_contact_thankyou_description_2:
    "If you have not received the email, please contact us again as this may be due to a mistake in the email address you entered or your email address settings may be set to receive spam.",
  project_contact_thankyou_page_title:
    "Thank you for contacting the project owner.",
  project_contact_name_placeholder: "e.g. V Matsuri Taro",
  project_contact_email_placeholder: "Example: <EMAIL>",
  project_contact_inqiury_placeholder: "Please enter your inquiry details.",
  contact_email_confirmation_details_4: `If you have specified a domain, please make sure that you can receive emails from "@v-sai.jp".`,
  contact_email_confirmation_details_5: `If your email address contains two or more consecutive periods "..." or a period "." immediately before "@", we will not be able to receive or reply to your email. Please contact us from a different email address.`,
  contact_email_confirmation_details_3:
    "Please note that it may take some time for us to respond.",
  contact_email_confirmation_details_1:
    "We will only reply to the email address you enter.",
  contact_email_confirmation_details_2:
    "If you are using a mobile carrier (au, Softbank, Docomo), iCloud mail, or Outlook mail, you may not receive emails. Please check your reception settings before contacting us.",
  contact_project_owner_warning:
    "Inappropriate language may result in restrictions on use or forced removal from the site.",
  search_bar_placeholder: "Enter the keyword you want to search for",
  find_campaign: "Find Campaign",
  find_vtuber: "Find VTuber",
  contact_owner: "Contact Owner",
  email_confirmation: "Email Confirmation",
  inquiry_details: "Inquiry Details",
  project_name: "Project Name",
  project_url_placeholder: "Example: Enter the URL of the project.",
  phone_number_placeholder: "Example: 090-1234-5678",
  contact_phone_desc: "Please enter with hyphens.",
  email_confirmation_error: "Emails do not match",
  items: "items",
  no_matching_information_for_keyword: `There are 0 VTubers that match the keywords you entered.
Please change your keywords and search again.`,
  having_trouble_searching:
    "If you are having trouble finding keywords to search for, try using the related keywords below.",
  search_note_1:
    "Alphanumeric characters are not case-sensitive and will produce the same search results.",
  search_note_2:
    "To find items that contain all of your keywords, search with spaces between them.",
  search_note_3:
    "Try searching for short, simple keywords with the same meaning, or general words instead.",
  search_note_4:
    "Please make sure you have entered your keywords correctly and there are no typos.",
  company_name_desc_2:
    "If you are an office or company, please include the name of your office or company.",
  company_name_desc_1: `If you are an individual, please write "Talent Name" or "Individual."`,
  project_url: "Project URL",
  about_campaign: "About Campaign",
  about_rewards: "About Rewards",
  company_name: "Company Name",
  please_read_before_sending: "Please read before sending",
  company_name_placeholder: "Example: V Festival",
  about_purchasing_plan: "About Purchasing Plan",
  others: "Others",
  inquiry_type: "Inqiury Type",
  name: "Name",
  Recent_Posts: "Recent Posts",
  new_vtuber: "New VTuber",
  please_check_back_later: "Please check back later.",
  cta_title: `Have fun with your idol
  Spend a special moment with your idol`,
  search_tips: "Search tips",
  cta_subtitle: `Your support can change your favorite Vtuber's future!
Join the V Festival and support your favorite Vtuber.
  `,
  top_participants: `Top
   Participants`,
  Max_sub_for_user: "Maximum time a user can subscribe",
  vtuber_categories: "Vtuber Categories",
  accepted_cards: "The following cards can be used.",
  not_accepting_subcriptions: "We do not currently offer membership plans",
  deleting: "deleting",
  replying: "Replying",
  supported_campaigns: `Supported
   Campaigns`,
  vtuber_not_accepting_subscription_message:
    "vtuber is not accepting subscriptions at the moment please check back later.",
  Create: "Create",
  phone_number: "Phone Number",
  preferred_delivery_date: "Preferred Delivery Date",
  preferred_delivery_time: "Preferred Delivery Time",
  recipient: "Recipient",
  subscription_not_active: "Subscription Not Active",
  under_maintainance_title: `Currently
  Under Maintainance`,
  support_page_title: `V Matsuri offers features that can be enjoyed
  by both Vtubers and fans.
  If you're unsure about how to use it, please check out this guide first.`,
  verifed: "Verifed",
  support_vtuber_guide: `For those who want to
  Find and support your favorite Vtuber`,
  about_vsai_description1: `V-matsuri (V-sai) is a fan-participation platform that combines "crowdfunding x virtual x festival" and allows Vtubers and fans to enjoy together. It provides a new form of entertainment space where Vtubers can broadcast their activities and fans can support them through voting and crowdfunding.`,
  member_only_and_vtuber_not_accepting_subscriptions_message:
    "it's a member only post and currently the vtuber is not accepting subscription.",
  Give_Point: "Give Point",
  Give_Platform_Points: "Give Platform Points",
  Unverifed: "Unverifed",
  Approve: "Approve",
  all: "All",
  coming_soon: "Coming Soon",
  enjoy_event_step_2: `Find the Vtuber 
  you want to support
  in the event`,
  enjoy_event_step_3: `Vote for the 
  vtuber you want
  to support
  using the 
  "Vote and Support"
  button`,
  annual_plan_paid_monthly: `Annual Plan
  (Paid monthly)`,
  about_vsai_description2: `V-matsuri offers "fan voting events" where fans can support events in which Vtubers participate by voting and commenting, and "event x crowdfunding" where Vtubers carry out crowdfunding projects and fans can support them to realize their dreams. "Create dreams with your favorite." 
  V-matsuri aims to be a place where Vtubers and fans can grow together and support each other.`,
  Reject: "Reject",
  your_video_will_be_here: "Your video will be here",
  v_festival_participate_guide: `For those who want to
  Participate in V Festival`,
  Remarks: "Remarks",
  Submit_Vote: "Submit Vote",
  Enter_your_remarks_and_points_to_vote_for_this_participation:
    "            Enter your remarks and points to vote for this participation.",
  Reject_Participation: "Reject Participation",
  Please_provide_a_reason_for_rejecting_this_participant:
    "Please provide a reason for rejecting this participant",
  subscription_filled: "Subscription Filled",
  You_Cannot_Vote_this_Participant: " You Cannot Vote this Participant",
  Are_you_sure_you_want_to_approve: "Are you sure you want to approve?",
  Requirements: "Requirements",
  created_by: "Created By",
  auth_banner_subtitle:
    "V-sai is a connection between Vtubers and fans,Participatory platform that will make your dreams come true.",
  auth_banner_title: "Welcome to the V Festival",
  register_button_text: "Register an account",
  community_guidelines: "Community Guidelines",
  crowdfunding_guidelines: "Crowdfunding Guidelines",
  transaction_act: "Transaction Act",
  email_address: "email address",
  auth_name_label: "name",
  auth_name_placeholder: "Yamada Taro",
  something_went_wrong: "Something went wrong",
  please_try_again: "Please try again",
  try_again: "Try again",
  verification_code: "Verification Code",
  select_video: "Select Video",
  drop_your_video_here: "Drop your video here",
  upload_image: "Upload Image",
  no_recommended_posts: "No recommended posts",
  subscribe_to_see_post: "Subscribe to see the post",
  profile_picture_ratio: "Ratio: 200px×200px",
  registered_email_address: "Registered email address",
  current_password: "current password",
  most_popular: "Most Popular",
  registered_credit_card: "Registered credit card",
  file_uploader_supported_video_formats: "Supports MP4, AVI, MOV, MKV",
  having_trouble_contact_support:
    "Having trouble? Contact support for assistance",
  change_email: "Change Email",
  update_email_for_your_account: "Update your email address for your account",
  update_email: "Update Email",
  verification_code_will_be_sent_to_new_email:
    "A verification code will be sent to your new email address.",
  verify_new_email: "Verify New Email",
  enter_6_digit_code:
    "Enter the 6-digit verification code sent to your new email",
  new_email: "New Email",
  email_update_successfully: "Email Updated Successfully!",
  email_verified_and_changed:
    "Your email address has been changed and verified.",
  verification_code_has_been_sent_to_your_new_email:
    "   A verification code has been sent to your new email address. Please enter the verification code you received in the email.",
  did_not_receive_code: "Didn't receive the code?",
  or: "or",
  movies_and_galleries: "Movies/Gallery",
  all_campaigns: "All Crowdfunding",
  event_list: "Event List",
  no_campaigns_for_this_vtuber: "No Campaigns for this Vtuber",
  User_Guide: "User Guide",
  login_with_other_services: "Login with other services",
  register_with_other_services: "Register with other services",
  whats_new: "What's New",
  recommended_posts: "Recommended posts",
  latest_events: "Latest events",
  creator_studio: "Creator Studio",
  points: "points",
  Delete_Privacy_Policy: "Delete Privacy Policy",
  vtuber_not_found: "Vtuber not found",
  Are_You_Sure: "Are you sure?",
  credit_card_addition_success_message: "Card added successfully",
  credit_card_deletion_error_message: "Unable to delete card",
  create_new_account: "Create a new acccount",
  v_offering_rewards: "V offering rewards",
  prize_benfits: "Prize/Benefits",
  verify_code: "Verify code",
  self_introduction: "Self introduction",
  search_bar_title: "Search for anything",
  add_to_favorites: "Add to favorites",
  remove_from_favorites: "Remove from favorites",
  paid_membership: "Paid Membership",
  monthly_plan: "Monthly Plan",
  participating_events: "Participating Events",
  target_amount: "Target amount",
  delete_privacy_policy_confirmation:
    "Are you sure you want to delete this privacy policy? This action cannot be undone.",
  No_Privacy_Policy: "No privacy policy content available",
  proceed: "Proceed",
  confirm: "Confirm",
  contact_page_title: "Contact the management",
  register_page_subtitle: "Register as a fan",
  yen: "¥",
  delete_credit_card_alert_title: "Are you absolutely sure?",
  delete_credit_card_alert_description:
    "This action cannot be undone. This will delete your current credit card information.",
  are_you_sure_you_want_to_leave: "Are you sure you want to leave?",
  changes_made_may_not_be_saved: "Changes you made may not be saved.",
  connecting_with_vtubers:
    "Connecting with Vtubers expands just by registering.",
  your_card_is_being_processed_please_wait:
    "Your card is being processed please wait....",
  register_password_description: "Half-width alphanumeric characters or more",
  password: "password",
  register_page_title: `Register as a new account
  (free)`,
  you_can_support_vtuber: "You can support your favorite Vtuber.",
  check_info_about_fav_idol: `You can quickly check the "information" of your favorite idol.`,
  you_can_participate_in_events: "You can participate in special events.",
  register_how_to_check_your_idol_info:
    "Register for the V Festival and you can quickly check out the latest news on events and crowdfunds. You can quickly get the events, activities and new initiatives of your favorite Vtuber.",
  delivery_information: "Delivery Information",
  account_information: "Account Information",
  notification_settings: "Notification Settings",
  invalid_dob: "Invalid date of birth",
  required: "required",
  change: "change",
  register_how_to_participate_in_special_events:
    "By registering for a fan account, you can participate in events sponsored by V-Sai and Vtuber crowdfans. Your support, including voting-based events and cheering projects, will help you make your favorite venue even more passionate.",
  register_how_to_support_vtuber:
    "By registering for a V-Sai fan account, you can support your favorite Vtuber in real life. Your cheers, such as voting, likes, and supporting crowdfans, will give you the power to push your favorites back.",
  register_terms_label:
    "By creating an account you agree to the Terms of Use and Privacy Policy.",
  forgot_password: "Forgot your password?",
  login_page_title: "Log In",
  login_with_apple: "Log in with Apple",
  login_with_facebook: "Log in with Facebook",
  login_with_google: "Log in with Google",
  login_with_X: "Log in with X",
  register_form_title: "Register your account information",
  register_form_subtitle: "Please enter your account information.",
  verify_page_title: "Enter confirmation code",
  auth_completed: "The authentication has been completed",
  auth_completed_message:
    "The authentication code has been confirmed. Please proceed to registering your account information.",
  register_account_info: "Register your account information",
  verify_page_subtitle:
    "Your verification code has been sent to the email address registered in your account. Please enter the verification code you received in the email.",
  login_page_subtitle: "Login as a Fan",
  login_register: "Login/Register",
  login_as_vtuber: "Log in as a Vtuber",
  sign_up: "Sign Up",
  sign_up_as_fan: "Sign up as a fan",
  submit: "Submit",
  forgot_password_page_subtitle: "Enter your email to reset your password",
  forgot_password_page_title: "Forgot your password?",
  already_have_a_code: "Already received a code?",
  send_verification_code: "Send Verification Code",
  send: "Send",
  verify: "Verify",
  enter_code: "enter code",
  verification_page_subtitle:
    "Your verification code has been sent to the email address registered in your account. Please enter the verification code you received in the email.",
  verification_page_title: "Enter Verification Code",
  new_password: "New Password",
  Save: "Save",
  verification_code_label: "Authentication Code",
  resend_verification_code: "Resend Verification Code",
  verification_code_placeholder: "Enter the XX digit code",
  return_to_top_page: "Return to top page",
  registration_finished: "Account registration Finished.",
  register_for_free: "Register for free",
  password_reset_success_message:
    "Password reset successfully. Login to continue",
  continue: "Continue",
  add_a_comment: "Add a comment",
  select_a_card: "Select a card",
  address_1: "Street Number",
  loading_cards: "Loading cards...",
  address_2: "Building name, room number, etc.",
  country: "Country",
  city: "City",
  postal_code: "Postal Code",
  per_year: "per year",
  purchase: "Purchase",
  annual_billing: "Annual Billing",
  state: "State",
  premier_plan: "Premium Plan",
  monthly_billing: "Monthly Billing",
  sign_up_for: "Sign up for",
  optional: "Optional",
  no_events_found_message:
    "sorry, we couldn't find the event you're looking for.",
  registration_finished_message:
    "Your V Festival account has been registered, so you can use the V Festival homepage. If you continue logging in like this, please proceed to the login screen.",
  welcome_page_title: "Account registration is complete.",
  what_is_v_festival: "What is V Festival?",
  see_details: "See details",
  limited_content: "Limited content",
  login_to_see_post: "Log in to see the post",
  view_paid_post: "View paid post",
  membership_only_content: "Membership-only content",
  no_events_for_this_vtuber: "No events for this vtuber",
  sign_up_to_see_post: "Register for free to view posts",
  welcome_page_subtitle:
    "Your V-Sai account registration is complete, so you can now use the V-Sai homepage. If you wish to log in now, please proceed to the login screen.",
  image_not_available: "Image not available",
  v_festival: "V Festival",
  start_registering_as_vtuber: "Start registering as a Vtuber for free",
  share_activities_with_fans:
    "Let's share your activities with even more fans!",
  challenge_ourselves_to_a_new_stage_v_festival:
    "Let's challenge ourselves to a new stage at V Festival!",
  your_support_will_change_vtuber_future:
    "Your support will change your favorite idol's future!",
  join_v_festival_and_support_vtuber:
    "Join the V Festival and support your favorite Vtubers.",
  begginers_guide_users: "Beginner's Guide (For Users)",
  beginners_gudie_vtuber: "Beginner's Guide (Vtuber)",
  popular_categories: "Popular Categories",
  popular_campaign: "Popular crowdfunding",
  see_more_events: "See more events",
  confirm_password: "Confirm Password",
  keyword_category_search: "Keyword/Category Search",
  confirm_password_error: "Passwords don't match",
  register_terms_conditions_error:
    "Please agree to the terms of service and privacy policy",
  year: "year",
  day: "day",
  month: "month",
  profile_image_success_message: "Profile image updated successfully",
  profile_picture: "Profile picture",
  date_of_birth: "Date of birth",
  register_form_dob_description:
    "*To protect minors, please enter your date of birth. *This will not be displayed to other users.",
  confirm_password_desccription:
    "*Please enter between 8 and 30 characters including uppercase and lowercase letters and numbers in half-width.",
  register_form_terms: "Receive campaign and announcement information by email",
  register_form_user_name_description:
    "*Only alphanumeric characters (uppercase/lowercase) and _ (underscore) can be used.",
  user_name: "Username",
  no_credit_card_message: "No credit card information is stored",
  registered_credit_card_info: "Registered credit card information",
  add_credit_card: "Add credit card",
  bank_account_information: "Bank account information",
  no_bank_details_message: "No bank details are stored",
  add_bank_details: "Add your bank account details",
  delivery_info_not_saved: "Delivery information is not saved",
  register_new: "Register new",
  receive_recommendation_info_label: "Receive recommendations from V Festival",
  receive_recommendation_info_desc:
    "*We will deliver event information, limited-time information, etc.",
  receive_supported_event_updates_label:
    "Receive updates about events you've supported",
  edit_your_profile: "Edit your profile",
  no_campaign_found_message:
    "sorry, we couldn't find the campaign you're looking for.",
  no_participating_vtubers_for_event:
    "There are currently no participating Vtubers for this event.",
  events_currently_participating: `Participating
   Events`,
  no_participating_vtubers: "No participating V-tubers",
  vertical_scroll: "Vertical Scroll",
  announcement: "Announcement",
  page_may_have_been_removed:
    "The page you are trying to access may have been removed, changed, or may be currently unavailable.",
  sorry_for_inconvenience:
    "Sorry for the inconvenience, but please go to the top of the V Festival homepage.",
  search_from_above_menu: "Or please search from the menu above.",
  back_to_top: "Back to top",
  favorite_vtuber: `Favorite
   Vtuber`,
  message: "Message",
  your_gallery_is_empty: "Your gallery is empty",
  login_to_like_post_message: "You need to login to like this post",
  like: "Like",
  liked: "Liked",
  all_voting_tickets: "All voting tickets",
  page_not_found_message: "The page you are looking for could not be found.",
  special_voting_ticket: "Special voting ticket",
  daily_voting_ticket: "Daily voting ticket",
  ticket: "ticket",
  no_particiapting_events: "You haven't participated in any events yet",
  no_supported_campaigns: "No supported campaigns",
  no_favorite_vtubers: "No favorite vtubers",
  event_category: "Event category",
  loading_comments: "Loading Comments...",
  receive_supported_event_updates_desc:
    "*You will receive email notifications when the event you supported or the participating Vtuber updates their page.",
  receive_favorite_event_updates_desc:
    "*You will receive email notifications when your favorite events or Vtubers update their pages.",
  receive_favorite_event_updates_label: "Get updates on your favorite events",
  receive_activity_reports_desc:
    "*You will receive email notifications when you support or favorite an event, or when a Vtuber updates their activity report.",
  receive_user_messages_desc:
    "*You will be notified by email when you receive a message from the owner of a crowdfunding or event you want to support, or when you receive a message from a backer on your crowdfunding.",
  receive_user_messages_label: "Receive messages from other users",
  receive_activity_reports_label: "Get updates on activity reports",
  update_reward_address_instruction:
    "*If there are any changes to the delivery address for your reward after supporting a project, please make the changes directly through the project you supported.",
  credit_card_delete_information:
    "*Even if you delete your credit card information from here after supporting a project, your support will not be canceled.",
  regulations: "Regulations",
  guidelines: "Guidelines",
  start_registering_for_free: "Start registering for free",
  operating_company: "Operating Company",
  terms_of_use: "Terms of Use",
  contact_readme:
    "As we are currently working remotely, we will contact you by email as needed. Please note that it may take some time for us to respond depending on the content of your inquiry.",
  legal_info: "Legal Information",
  sitemap: "Sitemap",
  privacy_policy: "Privacy Policy",
  support: "Support",
  more: "more",
  user_guides: "User Guides",
  home: "home",
  events: "events",
  keyword_search: "Keyword search",
  crowdfunding: "crowdfunding",
  beginners_guide: "beginners guide",
  search_by_keyword: "Search by keyword",
  faq: "faq",
  view_more: "view more",
  view_all: "view all",
  view_more_events: "view more events",
  see_more_vtubers: "See more vtubers",
  recommended_vtubers: "recommended vtubers",
  recommended_events: "recommended events",
  see_more_projects: "see more projects",
  no_notifications_message: "You don't have any notifications.",
  notifications: "notifications",
  loading_notifications: "Loading notifications",
  progress: "Progress",
  home_projects_description:
    "This is where the crowdfunding title will go. This text is a placeholder. It is inserted to check the font size, amount of text, letter spacing, line spacing, etc.",
  my_page: "My Page",
  settings: "Settings",
  my_events: "My Events",
  logout: "Logout",
  free_registration: "Free Registration",
  sign_in: "sign in",
  logout_modal_title: "Are you sure you want to log out?",
  logout_modal_description:
    "You will be signed out of your account. You can sign back in at any time.",
  cancel: "Cancel",
  stay_in: "Stay in",
  change_email_address: "Change Email Address",
  change_password: "Change Password",
  change_payment_method: "Change Payment Method",
  edit_profile: "Edit Profile",
  link_external_account: "Link External Account",
  account_settings: "Account Settings",
  didnt_make_this_change:
    " If you didn't make this change, please contact support immediately.",
  apply: "apply",
  cancel_upload: "cancel upload",
  clear: "clear",
  click_to_view_image: "click to view image",
  crop_image: "crop image",
  recrop: "recrop",
  select_another: "select another",
  round: "round",
  square: "square",
  shape: "shape",
  aspect_ratio: "aspect ratio",
  back_to_edit: "back to edit",
  upload: "upload",
  uploading: "uploading",
  please_wait: "please wait",
  your_image_will_be_here: "your image will be here",
  udpate_profile: "update profile",
  update_password: "update password",
  update: "Update",
  my_profile: "my profile",
  your_name: "your name",
  drop_your_image_here: "Select Image",
  or_click_to_browse: "or click to browse",
  file_uploader_supported_formats: "Supports JPG, PNG, JPEG",
  select_image: "Select Image",
  only_images_are_allowed: "only images are allowed",
  please_wait_for_the_file_to_finish_uploading:
    "please wait for the file to finish uploading",
  any_changes_you_made_will_be_lost: "any changes you made will be lost",
  your_file_is_being_uploaded: "your file is being uploaded",
  creator: "creator",
  email_verified: "email verified",
  joined_on: "joined on",
  enter_payment_info: "Enter Payment Information",
  credit_card: "Credit Card",
  use: "use",
  delete: "delete",
  valid_thru: "valid thru",
  payment_method: "Payment Method",
  credit_card_information: "Credit Card Information",
  card_number: "Card Number",
  expiration_date: "Expiration Date",
  save_this_info_for_future_purchases:
    "Save this information for future purchases",
  pay_now: "Pay Now",
  price: "Price",
  subtotal: "Subtotal",
  total: "Total",
  includes_10_percent_tax: "Includes 10% tax",
  new_vtubers: "New Vtubers",
  see_list: "see list",
  add_billing_info: "Add new billing info",
  edit_billing_info: "Edit billing info",
  credit_card_notice_title: "Important Payment Information",
  credit_card_notice_description:
    "we don't store card information on our server and only 4 credit cards can be registered.",
  card_holder_name: "Card Holder Name",
  paypal: "PayPal",
  add_event: "Add Event",
  no_events: "No events",
  loading: "Loading",
  rules: "Rules",
  subscription_success_message: "Subscription added successfully",
  event_title: "Event Title",
  description: "Description",
  event_banner: "Event Banner",
  category: "Category",
  short_description: "Short Description",
  start_date: "Start Date",
  end_date: "End Date",
  create_event: "Create Event",
  event_created_successfully: "Event created successfully",
  event_category_placeholder: "select a category for your event",
  event_title_placeholder: "enter your event title",
  event_description_placeholder: "enter your event description",
  event_rules_placeholder: "enter your event rules",
  event_start_date_placeholder: "select a start date for your event",
  event_end_date_placeholder: "select a end date for your event",
  short_description_placeholder: "enter your event short description",
  get_started_for_free: "Get started for free",
  and: "and",
  excitement: "Excitement",
  "&": "&",
  project_search: "Project Search",
  home_banner_text_one:
    "#VFestival is a place where Vtubers and fans can connect",
  manage_your_delivery_address: "Manage your delivery address",
  home_banner_text_two: "A participatory platform to realize dreams",
  search: "Search",
  category_search: "Category Search",
  prefectures: "Prefecture",
  participating_vtubers: "Participating VTubers",
  best_award: "Best Award",
  desing_award: "Design Award",
  v_festival_award: "V Festival Award",
  close: "close",
  redirecting_to_the_app: "Please wait while we redirect you to the app..",
  thankyou_for_voting: "Thank you for voting!",
  you_have_voted_with: "You have voted with the following number of votes.",
  event_rules: "Event Rules",
  comments: "Comments",
  final_votes: "Final Votes",
  participating_vtuber_recruitment_information:
    "Participating VTuber Recruitment Information",
  no_comments_yet: "No comments yet.",
  vote: "Vote",
  already_voted: "Already Voted",
  event_posts: "Event Posts",
  comment: "Comment",
  post_not_found: "Post Not Found",
  login_to_comment: "Login to comment",
  list_of_vtubers: "List of VTubers",
  share_vtuber_profile: "Share VTuber Profile",
  favorite: "Favorite",
  upgrade: "Upgrade",
  become_a_member: "Become a Member",
  currently_in_use: "Currently in use",
  membership_fees: "Membership Fees",
  search_post: "Search Post",
  by_year: "By Year",
  member_rank: "Member Rank",
  sort_by: "Sort By",
  no_posts_available: "No posts available",
  see_more: "see more",
  see_less: "see less",
  login_to_see_the_post: "Login to see the post",
  register_to_see_the_post: "Register for free and view posts",
  subscription_added_successfully: "Subscription added successfully",
  personal_overview: "Personal Overview",
  gallery: "Gallery",
  membership: "Membership",
  voting_completed: "Voting completed",
  position: "Position",
  limit_reached: "Subscription limit reached",
  vote_with_selected_balot: "Vote with the selected ballot",
  daily_voting_not_avaialble:
    "Daily voting not available because you have already voted for today.",
  select_one_voting_right: "Please select either all or partial points",
  more_than_available_points_error: "can't use more than available points",
  partial_voting_error_message: "please enter voting points",
  view_vtubers_profile: "View VTuber's Profile",
  vote_and_support: "Vote and support",
  select_ticket: "Select a ticket",
  daily_voting_ticket_info:
    "Daily voting tickets can only be used for one vote per day.",
  special_voting_ticket_info:
    "You can choose any number of votes using the special voting ticket.",
  exampale_vote_info: "Example: 1 vote for every 500 yen",
  use_special_voting_right: "Use special voting rights",
  use_all_special_voting_right: "Use all available special voting tickets",
  use_partial_special_voting_right: "Use some of the special tickets",
  use_daily_voting_rights: "Use the daily voting ticket",
  crowdfunding_list: "Crowdfunding List",
  search_by_category: "Search by category",
  search_by_vtuber: "Search by Vtuber",
  register: "Register",
  for_fans: "For Fans",
  for_vtubers: "For VTubers",
  how_to_enjoy_event: "How to enjoy the voting event",
  event_participation_requirements: "Participation requirements｜※Please read※",
  event_overview: "Event overview",
  register_as_vtuber: "Register as VTuber",
  rearrange_vtubers: "Rearrange Vtubers",
  view_profile: "View profile",
  login_to_vote: "Login to vote",
  number_of_votes: "Number of votes",
  votes: "votes",
  announcement_of_event_results: "Announcement of results",
  activity_report: "Activity Report",
  project_introduction: "Project Introduction",
  supporters: "Supporters",
  select_by_return: "Select by return",
  number_of_supporters: "Number of Supporters",
  particiaption_flow: "Participation flow",
  support_this_project: "Support this project",
  current_support_amount: "Current Support Amount",
  no_of_supporters: "No. of Supporters",
  see_recommended_projects: "See recommended projects",
  support_money_donation_amount: "Support Money / Donation Amount",
  choose_by_reward_returns: "Choose by Rewards/Returns",
  back: "back",
  enter_verification_code_sent_to_your_email:
    "Enter the 6-digit verification code sent to your email",
  code_expires_in: "Code expires in",
  code_expired: "Code expired",
  please_check: "please check",
  accept_all_cookies: "Accept all cookies",
  reject_all: "Reject all",
  campaing_ended: "campaign ended",
  starting_in: "starts in",
  cookies_alert_title:
    "We use cookies to improve your experience on our website and analyze our website traffic.",
  pip_not_supported_message:
    "Your browser does not support Picture in Picture mode. Please use a different browser or right-click the video to enable Picture in Picture.",
  enter_fullscreen: "Enter Fullscreen",
  exit_fullscreen: "Exit Fullscreen",
  enter_picture_in_picture_mode: "Enter Picture-in-Picture Mode",
  exit_picture_in_picture_mode: "Exit Picture-in-Picture Mode",
  go_back: "go back",
  rest: "rest",
  days: "days",
  events_list: "Events List",
  login_to_add_events: "Login to add events",
  not_found_page_subtitle:
    "The page you tried to access may have been deleted, moved, or is currently unavailable. We apologize for the inconvenience. Please visit the V Festival homepage top page or use the menu above to search for the desired page.",
  not_found_page_title: "The page you are looking for could not be found.",
  v_festival_home_page: "V Festival Homepage",
  requested_url_not_found: "The requested URL was not found on this server.",
  notfound_404: "404 Not Found",
  view: "view",
  mark_as_unread: "mark as unread",
  mark_read: "mark read",
  we_hope_you_understand: "We hope you understand.",
  thankyou_for_your_patience: "Thank you for your patience.",
  new: "new",
  thankyou_for_using_vsai: "Thank you very much for using V Festival.",
  maintainance_description:
    "Due to system maintenance currently being performed, V Festival is currently unavailable.",
  maintainance_wait_message:
    "We apologize for the inconvenience, but please wait until maintenance is complete.",
  maitanance_estimated_time: "Estimated end of maintenance",
  thankyou_for_your_understanding_and_cooperation:
    "Thank you for your understanding and cooperation.",
  estimated_duration_time: "January 1, 2025 (Wednesday) around 09:00",
  upload_cancelled: "upload cancelled",
  CATEGORY_NAME: "Category Name",
  CATEGORY_DESCRIPTION: "Category Description",
  TOTAL_BUDGET: "Total Budget",
  END_DATE: "End Date",
  EDIT: "Edit",
  ADD_CAMPAIGN: "Add Campaign",
  CAMPAIGNS: "Campaigns",
  CAMPAIGN_NAME: "Campaign Name",
  CAMPAIGN_DESCRIPTION: "Campaign Description",
  CAMPAIGN_SHORT_DESCRIPTION: "Campaign Short Description",
  START_DATE: "Start Date",
  ADD_THUMBNAIL: "Add Thumbnail",
  Category: "Category",
  Banners: "Banners",
  Variants: "Variants",
  Create_Campaign: "Create Campaign",
  No_Banners: "No Banners",
  Select_File: "Select File",
  No_Variants: "No Variants",
  Drag_And_Drop: "Drag And Drop",
  Drop_The_file_to_upload: "Drop The file to upload",
  Any_changes_you_made_may_not_be_saved:
    "Any changes you made may not be saved",
  Pick_A_Date: "Pick A Date",
  Add_Post: "Add Post",
  Posts: "Posts",
  Author: "Author",
  All_Events: "All Events",
  My_Events: "My Events",
  Participated_Events: "Participated Events",
  Events: "Events",
  Add_Event: "Add Event",
  Delete: "Delete",
  Create_Event_Post: "Create Event Post",
  Participated: "Participated",
  Participate: "Participate",
  Hide_Replies: "Hide Replies",
  View_Replies: "View Replies",
  No_Relies_Yet: "No Replies Yet",
  Profile_Settings: "Profile Settings",
  Update_your_VTuber_profile_information:
    "Update your VTuber profile information",
  Basic_Info: "Basic Info",
  Media: "Media",
  Social_Links: "Social Links",
  Basic_Information: "Basic Information",
  Display_Name: "Display Name",
  Furigana: "Furigana",
  Enter_furigana: "Enter furigana",
  Tell_your_fans_about_yourself: "Tell your fans about yourself",
  Profile_Media: "Profile Media",
  Image: "Image",
  Banner_Image: "Banner Image",
  Save_Profile_Changes: "Save Profile Changes",
  Profile_Preview: "Profile Preview",
  See_How_Your_Profile_Appears_To_Others:
    "See how your profile appears to others",
  Instagram: "Instagram",
  TikTok: "TikTok",
  Youtube: "Youtube",
  X: "X",
  Vtuber: "Vtuber",
  User: "User",
  No_Events: "No Events",
  Approved: "Approved",
  Create_Post: "Create Post",
  Enter_Details_To_Create_Your_Post: "Enter Details To Create Your Post",
  Post_Name: "Post Name",
  Video: "Video",
  Media_Type: "Media Type",
  Membership_Only: "Membership Only",
  Post_Title: "Post Title",
  Uploading_Media: "Uploading Media...",
  View: "View",
  Created_At: "Created At",
  d_left: "d left",
  Ending_Soon: "Ending Soon",
  Plan_Title: "Plan Title",
  Engagement: "Engagement",
  Plan_Details: "Plan Details",
  Share: "Share",
  Premium_Plan: "Premium Plan",
  Status: "Status",
  Active: "Active",
  Expired: "Expired",
  Monthly_Revenue: "Monthly Revenue",
  Retention_Rate: "Retention Rate",
  Active_retention: "Active retention",
  Index: "Index",
  Id: "Id",
  Add: "Add",
  per_month: "Per Month",
  Active_Members: "Active Members",
  Plans: "Plans",
  Subscribers: "Subscribers",
  No_Subscribers_Yet: "No Subscribers Yet",
  Details: "Details",
  No_Variants_Available: "No Variants Available",
  Max_Sub: "Max Sub",
  Posts_Analytics: "Posts Analytics",
  Cancel: "Cancel",
  Reply: "Reply",
  Editing: "Editig",
  Campaign_Analytics: "Campaign Analytics",
  Campaign_Not_Found: "Campaign Not Found",
  Current_Subscriptions: "Current Subscriptions",
  Max_Subscriptions: "Max Subscrptions",
  Category_Name: "Category Name",
  Add_Category: "Add Category",
  Dashboard: "Dashboard",
  Users: "Users",
  Categories: "Categories",
  Craetor_Request: "Creator Request",
  FAQ: "FAQ",
  Actions: "Actions",
  Name: "Name",
  Email: "Email",
  subscribed: "subscribed",
  Expires_On: "Expires On",
  Title: "Title",
  Add_Image: "Add Image",
  Update_Variant: "Update Variant",
  Add_Variant: "Add Variant",
  Display_Order: "Display Order",
  Update_Banner: "Update Banner",
  Add_Banner: "Add Banner",
  index: "index",
  Edit_Campaign: "Edit Campaign",
  Update_the_details_of_your_campaign: "Update the details of your campaign",
  Update_Campaign: "Update Campaign",
  Total_Revenue: "Total Revenue",
  Compared_To_Last_Month: "Compared To Last Month",
  Target: "Target",
  Raised: "Raised",
  Goal: "Goal",
  Update_Post: "Update Post",
  Rejected: "Rejected",
  Pending: "Pending",
  Campaign_Progress: "Campaign Progress",
  time: "Time",
  Location: "Location",
  Online_Virtual: "Online (Virtual)",
  Prizes: "Prizes",
  First_Place: "1st Place",
  Featured_Spotlight: "Featured Spotlight",
  Second_Place: "2nd Place",
  Platform_promotion: "Platform Promotion",
  Third_Place: "3rd Place",
  Special_Mention: "Special Mention",
  Participants: "Participants",
  About: "About",
  discussion: "discussion",
  Analytics: "Analytics",
  Event_Analytics: "Event Analytics",
  Add_a_comment: "Add a comment",
  No_Comments: "No comments yet",
  Comment_as: "Comment as",
  Upgrade_To_Pro: "Upgrade To Pro",
  Account: "Account",
  Select_Language: "Select Language",
  English: "English",
  Japanese: "Japanese",
  Billing: "Billing",
  Funding_Progress: "Funding Progress",
  Enter_Details: "Enter the details below to create your Campaign",
  EDIT_POST: "Edit Your Post",
};
