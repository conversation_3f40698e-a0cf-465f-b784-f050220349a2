import { useLanguage } from "@vtuber/language/hooks";
import { cn } from "../lib/utils";

interface Props {
  className?: string;
  type?: "required" | "optional";
}

export const InputBadge = ({ type = "required", className }: Props) => {
  const { getText } = useLanguage();
  return (
    <div
      className={cn(
        "text-xs font-bold  py-px px-2 text-center rounded-[2px] text-white",
        type === "required" ? "bg-red02" : "bg-[#898989]",
        className,
      )}>
      {getText(type)}
    </div>
  );
};
