import { AnimatePresence, motion, Variants } from "motion/react";
import React, {
  Children,
  createContext,
  ReactNode,
  useEffect,
  useRef,
  useState,
} from "react";
import { cn } from "../lib/utils";
import { Button, ButtonProps } from "./button";

interface StepperContextType {
  currentStep: number;
  direction: number;
  isCompleted: boolean;
  isLastStep: boolean;
  totalSteps: number;
  setDirection: React.Dispatch<React.SetStateAction<number>>;
  setTotalSteps: React.Dispatch<React.SetStateAction<number>>;
  updateStep: (newStep: number) => void;
  handleBack: () => void;
  handleNext: () => void;
  handleComplete: () => void;
}

interface StepperProps {
  children: ReactNode;
  currentStep: number;
  onStepChange: (step: number) => void;
  onFinalStepCompleted?: () => void;
  className?: string;
}

const StepperContext = createContext<StepperContextType | null>(null);

const useStepperContext = () => {
  const context = React.useContext(StepperContext);
  if (!context) {
    throw new Error("useStepper must be used within a Stepper");
  }
  return context;
};
function Stepper({
  children,
  currentStep = 1,
  onStepChange = () => {},
  onFinalStepCompleted = () => {},
  className,
}: StepperProps) {
  const [direction, setDirection] = useState<number>(0);
  const [totalSteps, setTotalSteps] = useState<number>(0);

  const isCompleted = currentStep > totalSteps;
  const isLastStep = currentStep === totalSteps && totalSteps > 0;

  const updateStep = (newStep: number) => {
    onStepChange(newStep);
    if (newStep > totalSteps) {
      onFinalStepCompleted();
    } else {
      onStepChange(newStep);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setDirection(-1);
      updateStep(currentStep - 1);
    }
  };

  const handleNext = () => {
    if (!isLastStep) {
      setDirection(1);
      updateStep(currentStep + 1);
    }
  };

  const handleComplete = () => {
    setDirection(1);
    updateStep(totalSteps + 1);
  };

  return (
    <StepperContext.Provider
      value={{
        currentStep,
        isCompleted,
        direction,
        totalSteps,
        setDirection,
        setTotalSteps,
        updateStep,
        handleBack,
        handleNext,
        handleComplete,
        isLastStep,
      }}>
      <div className={className}>{children}</div>
    </StepperContext.Provider>
  );
}

interface StepContentWrapperProps {
  children: ReactNode;
  className?: string;
}

function StepperContent({ children, className = "" }: StepContentWrapperProps) {
  const [parentHeight, setParentHeight] = useState<number>(0);
  const [isMeasured, setIsMeasured] = useState<boolean>(false);
  const { currentStep, isCompleted, direction, setTotalSteps } =
    useStepperContext();

  const stepsArray = Children.toArray(children);

  useEffect(() => {
    setTotalSteps(stepsArray.length);
  }, [stepsArray.length, setTotalSteps]);

  return (
    <motion.div
      style={{
        position: "relative",
        overflow: "hidden",
        height: isMeasured ? undefined : "auto",
      }}
      animate={isMeasured ? { height: isCompleted ? 0 : parentHeight } : {}}
      transition={{ type: "spring", duration: 0.4 }}
      className={className}>
      <AnimatePresence
        initial={false}
        mode="sync"
        custom={direction}>
        {!isCompleted && (
          <SlideTransition
            key={currentStep}
            direction={direction}
            onHeightReady={(h) => {
              setParentHeight(h);
              setIsMeasured(true);
            }}>
            {stepsArray[currentStep - 1]}
          </SlideTransition>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

interface SlideTransitionProps {
  children: ReactNode;
  direction: number;
  onHeightReady: (height: number) => void;
}

function SlideTransition({
  children,
  direction,
  onHeightReady,
}: SlideTransitionProps) {
  const containerRef = useRef<HTMLDivElement | null>(null);

  React.useEffect(() => {
    const el = containerRef.current;
    if (!el) return;

    const measure = () => {
      onHeightReady(el.offsetHeight);
    };

    measure();

    const ro = new ResizeObserver(() => measure());
    ro.observe(el);

    const imgs = Array.from(el.querySelectorAll("img"));
    imgs.forEach((img) => {
      if (!img.complete) img.addEventListener("load", measure);
    });

    return () => {
      ro.disconnect();
      imgs.forEach((img) => img.removeEventListener("load", measure));
    };
  }, [children, onHeightReady]);

  return (
    <motion.div
      ref={containerRef}
      custom={direction}
      variants={stepVariants}
      initial={direction === 0 ? "center" : "enter"}
      animate="center"
      exit="exit"
      transition={{ duration: 0.4 }}
      style={{ position: "absolute", left: 0, right: 0, top: 0 }}>
      {children}
    </motion.div>
  );
}

const stepVariants: Variants = {
  enter: (dir: number) => ({
    x: dir >= 0 ? "-100%" : "100%",
    opacity: 0,
  }),
  center: {
    x: "0%",
    opacity: 1,
  },
  exit: (dir: number) => ({
    x: dir >= 0 ? "50%" : "-50%",
    opacity: 0,
  }),
};

function StepItem(props: React.ComponentProps<"div">) {
  return <div {...props} />;
}

interface StepIndicatorProps {
  disable?: boolean;
  value: number;
  children?: (props: {
    isComplete: boolean;
    isActive: boolean;
    isInactive: boolean;
  }) => React.ReactNode;
  className?: string;
  wrapperClassName?: string;
}

function StepIndicator({
  disable = false,
  value,
  children,
  className,
  wrapperClassName,
}: StepIndicatorProps) {
  const { currentStep, setDirection, updateStep } = useStepperContext();
  const status =
    currentStep === value
      ? "active"
      : currentStep < value
        ? "inactive"
        : "complete";

  const handleClick = () => {
    if (value !== currentStep && !disable) {
      setDirection(value > currentStep ? 1 : -1);
      updateStep(value);
    }
  };

  return (
    <motion.div
      onClick={handleClick}
      className={cn(
        "relative outline-none focus:outline-none",
        !disable && value !== currentStep && "cursor-pointer",
        disable && "cursor-not-allowed",
        wrapperClassName,
      )}
      animate={status}
      initial={false}>
      <motion.div
        variants={{
          inactive: { scale: 1, backgroundColor: "#222", color: "#a3a3a3" },
          active: { scale: 1, backgroundColor: "#5227FF", color: "#5227FF" },
          complete: { scale: 1, backgroundColor: "#5227FF", color: "#3b82f6" },
        }}
        transition={{ duration: 0.3 }}
        className={cn(
          "flex h-8 w-8 items-center justify-center rounded-full font-semibold",
          className,
        )}>
        {children ? (
          children({
            isComplete: status === "complete",
            isActive: status === "active",
            isInactive: status === "inactive",
          })
        ) : status === "complete" ? (
          <CheckIcon className="h-4 w-4 text-black" />
        ) : status === "active" ? (
          <div className="h-3 w-3 rounded-full bg-[#060010]" />
        ) : (
          <span className="text-sm">{value}</span>
        )}
      </motion.div>
    </motion.div>
  );
}

interface StepConnectorProps {
  isComplete: boolean;
  className?: string;
}

function StepConnector({ isComplete, className }: StepConnectorProps) {
  const lineVariants: Variants = {
    incomplete: { width: 0, backgroundColor: "transparent" },
    complete: { width: "100%", backgroundColor: "#5227FF" },
  };

  return (
    <div
      className={cn(
        "relative mx-2 h-0.5 flex-1 overflow-hidden rounded bg-neutral-600",
        className,
      )}>
      <motion.div
        className="absolute left-0 top-0 h-full"
        variants={lineVariants}
        initial={false}
        animate={isComplete ? "complete" : "incomplete"}
        transition={{ duration: 0.4 }}
      />
    </div>
  );
}

interface CheckIconProps extends React.SVGProps<SVGSVGElement> {}

function CheckIcon(props: CheckIconProps) {
  return (
    <svg
      {...props}
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      viewBox="0 0 24 24">
      <motion.path
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{
          delay: 0.1,
          type: "tween",
          ease: "easeOut",
          duration: 0.3,
        }}
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M5 13l4 4L19 7"
      />
    </svg>
  );
}

const StepIndicatorList = ({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) => {
  const stepsArray = Children.toArray(children);
  const { currentStep } = useStepperContext();

  return (
    <div className={cn("flex w-full items-center p-8", className)}>
      {stepsArray.map((stepIndicator, index) => {
        const stepNumber = index + 1;
        const isNotLastStep = index < stepsArray.length - 1;
        return (
          <React.Fragment key={stepNumber}>
            {stepIndicator}
            {isNotLastStep && (
              <StepConnector isComplete={currentStep > stepNumber} />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

const StepPrevButton = ({ ...props }: ButtonProps) => {
  const { currentStep, handleBack } = useStepperContext();

  return (
    <Button
      {...props}
      onClick={(e) => {
        handleBack();
        props?.onClick?.(e);
      }}
      disabled={currentStep === 1}
      className={cn(
        currentStep <= 1 && "pointer-events-none opacity-50",
        props.className,
      )}
    />
  );
};

const StepNextButton = ({ children, ...props }: ButtonProps) => {
  const { handleNext, handleComplete, isLastStep } = useStepperContext();

  const handleClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    props?.onClick?.(e);
    if (isLastStep) {
      handleComplete();
    } else {
      handleNext();
    }
  };

  return (
    <Button
      {...props}
      onClick={(e) => handleClick(e)}>
      {children || (isLastStep ? "Complete" : "Next")}
    </Button>
  );
};

export {
  StepConnector,
  StepIndicator,
  StepIndicatorList,
  StepItem,
  StepNextButton,
  Stepper,
  StepperContent,
  StepPrevButton,
};
