// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file cms/v1/contact.proto (package api.cms.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { GenericResponseSchema } from "../../shared/v1/generic_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";

/**
 * Describes the file cms/v1/contact.proto.
 */
export const file_cms_v1_contact: GenFile =
  /*@__PURE__*/
  fileDesc(
    "ChRjbXMvdjEvY29udGFjdC5wcm90bxIKYXBpLmNtcy52MSJ7Cg5Db250YWN0UmVxdWVzdBIMCgRuYW1lGAEgASgJEhMKC3Byb2plY3ROYW1lGAIgASgJEhMKC2lucXVpcnlUeXBlGAMgASgJEg0KBWVtYWlsGAQgASgJEhYKDmlucXVpcnlEZXRhaWxzGAUgASgJEgoKAmlkGAYgASgJIqoBChNBZG1pbkNvbnRhY3RSZXF1ZXN0EhMKC2NvbXBhbnlOYW1lGAEgASgJEgwKBG5hbWUYAiABKAkSEgoKcHJvamVjdFVybBgDIAEoCRITCgtpbnF1aXJ5VHlwZRgEIAEoCRINCgVlbWFpbBgFIAEoCRIUCgdwaG9uZU5vGAYgASgJSACIAQESFgoOaW5xdWlyeURldGFpbHMYByABKAlCCgoIX3Bob25lTm8yqQIKDkNvbnRhY3RTZXJ2aWNlEl4KGFNlbmRDYW1wYWlnbkNvbnRhY3RFbWFpbBIaLmFwaS5jbXMudjEuQ29udGFjdFJlcXVlc3QaHi5hcGkuc2hhcmVkLnYxLkdlbmVyaWNSZXNwb25zZSIGgrUYAggBElsKFVNlbmRFdmVudENvbnRhY3RFbWFpbBIaLmFwaS5jbXMudjEuQ29udGFjdFJlcXVlc3QaHi5hcGkuc2hhcmVkLnYxLkdlbmVyaWNSZXNwb25zZSIGgrUYAggBEloKFVNlbmRBZG1pbkNvbnRhY3RFbWFpbBIfLmFwaS5jbXMudjEuQWRtaW5Db250YWN0UmVxdWVzdBoeLmFwaS5zaGFyZWQudjEuR2VuZXJpY1Jlc3BvbnNlIgBCLFoqZ2l0aHViLmNvbS9uc3AtaW5jL3Z0dWJlci9hcGkvY21zL3YxO2Ntc3YxYgZwcm90bzM",
    [file_authz_v1_authz, file_shared_v1_generic],
  );

/**
 * @generated from message api.cms.v1.ContactRequest
 */
export type ContactRequest = Message<"api.cms.v1.ContactRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string projectName = 2;
   */
  projectName: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string inquiryType = 3;
   */
  inquiryType: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string email = 4;
   */
  email: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string inquiryDetails = 5;
   */
  inquiryDetails: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string id = 6;
   */
  id: string;
};

/**
 * Describes the message api.cms.v1.ContactRequest.
 * Use `create(ContactRequestSchema)` to create a new message.
 */
export const ContactRequestSchema: GenMessage<ContactRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_contact, 0);

/**
 * @generated from message api.cms.v1.AdminContactRequest
 */
export type AdminContactRequest = Message<"api.cms.v1.AdminContactRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string companyName = 1;
   */
  companyName: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string projectUrl = 3;
   */
  projectUrl: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string inquiryType = 4;
   */
  inquiryType: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string email = 5;
   */
  email: string;

  /**
   * @generated from field: optional string phoneNo = 6;
   */
  phoneNo?: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string inquiryDetails = 7;
   */
  inquiryDetails: string;
};

/**
 * Describes the message api.cms.v1.AdminContactRequest.
 * Use `create(AdminContactRequestSchema)` to create a new message.
 */
export const AdminContactRequestSchema: GenMessage<AdminContactRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_contact, 1);

/**
 * @generated from service api.cms.v1.ContactService
 */
export const ContactService: GenService<{
  /**
   * @generated from rpc api.cms.v1.ContactService.SendCampaignContactEmail
   */
  sendCampaignContactEmail: {
    methodKind: "unary";
    input: typeof ContactRequestSchema;
    output: typeof GenericResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.ContactService.SendEventContactEmail
   */
  sendEventContactEmail: {
    methodKind: "unary";
    input: typeof ContactRequestSchema;
    output: typeof GenericResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.ContactService.SendAdminContactEmail
   */
  sendAdminContactEmail: {
    methodKind: "unary";
    input: typeof AdminContactRequestSchema;
    output: typeof GenericResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_cms_v1_contact, 0);
