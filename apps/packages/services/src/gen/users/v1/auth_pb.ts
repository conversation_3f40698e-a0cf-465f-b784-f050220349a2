// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file users/v1/auth.proto (package api.users.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { VtuberProfile } from "../../vtubers/v1/vtuberprofiles_pb";
import { file_vtubers_v1_vtuberprofiles } from "../../vtubers/v1/vtuberprofiles_pb";
import type { User } from "./users_pb";
import { file_users_v1_users } from "./users_pb";

/**
 * Describes the file users/v1/auth.proto.
 */
export const file_users_v1_auth: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_users_v1_users,
      file_vtubers_v1_vtuberprofiles,
    ],
  );

/**
 * @generated from message api.users.v1.SignInWithSocialRequest
 */
export type SignInWithSocialRequest =
  Message<"api.users.v1.SignInWithSocialRequest"> & {
    /**
     * @generated from field: string provider = 1;
     */
    provider: string;

    /**
     * @generated from field: string code = 2;
     */
    code: string;
  };

/**
 * Describes the message api.users.v1.SignInWithSocialRequest.
 * Use `create(SignInWithSocialRequestSchema)` to create a new message.
 */
export const SignInWithSocialRequestSchema: GenMessage<SignInWithSocialRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 0);

/**
 * @generated from message api.users.v1.SignInWithSocialResponse
 */
export type SignInWithSocialResponse =
  Message<"api.users.v1.SignInWithSocialResponse"> & {
    /**
     * @generated from field: string access_token = 1;
     */
    accessToken: string;

    /**
     * @generated from field: string refresh_token = 2;
     */
    refreshToken: string;
  };

/**
 * Describes the message api.users.v1.SignInWithSocialResponse.
 * Use `create(SignInWithSocialResponseSchema)` to create a new message.
 */
export const SignInWithSocialResponseSchema: GenMessage<SignInWithSocialResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 1);

/**
 * @generated from message api.users.v1.GetSessionRequest
 */
export type GetSessionRequest = Message<"api.users.v1.GetSessionRequest"> & {};

/**
 * Describes the message api.users.v1.GetSessionRequest.
 * Use `create(GetSessionRequestSchema)` to create a new message.
 */
export const GetSessionRequestSchema: GenMessage<GetSessionRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 2);

/**
 * @generated from message api.users.v1.GetSessionResponse
 */
export type GetSessionResponse = Message<"api.users.v1.GetSessionResponse"> & {
  /**
   * @generated from field: api.users.v1.User user = 1;
   */
  user?: User;

  /**
   * @generated from field: api.vtubers.v1.VtuberProfile vtuber = 2;
   */
  vtuber?: VtuberProfile;
};

/**
 * Describes the message api.users.v1.GetSessionResponse.
 * Use `create(GetSessionResponseSchema)` to create a new message.
 */
export const GetSessionResponseSchema: GenMessage<GetSessionResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 3);

/**
 * @generated from message api.users.v1.SignOutRequest
 */
export type SignOutRequest = Message<"api.users.v1.SignOutRequest"> & {};

/**
 * Describes the message api.users.v1.SignOutRequest.
 * Use `create(SignOutRequestSchema)` to create a new message.
 */
export const SignOutRequestSchema: GenMessage<SignOutRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 4);

/**
 * @generated from message api.users.v1.SignOutResponse
 */
export type SignOutResponse = Message<"api.users.v1.SignOutResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.SignOutResponse.
 * Use `create(SignOutResponseSchema)` to create a new message.
 */
export const SignOutResponseSchema: GenMessage<SignOutResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 5);

/**
 * @generated from message api.users.v1.SendSignupEmailVerificationCodeRequest
 */
export type SendSignupEmailVerificationCodeRequest =
  Message<"api.users.v1.SendSignupEmailVerificationCodeRequest"> & {
    /**
     * @gotag: validate:"required,email"
     *
     * @generated from field: string email = 1;
     */
    email: string;
  };

/**
 * Describes the message api.users.v1.SendSignupEmailVerificationCodeRequest.
 * Use `create(SendSignupEmailVerificationCodeRequestSchema)` to create a new message.
 */
export const SendSignupEmailVerificationCodeRequestSchema: GenMessage<SendSignupEmailVerificationCodeRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 6);

/**
 * @generated from message api.users.v1.SendSignupEmailVerificationCodeResponse
 */
export type SendSignupEmailVerificationCodeResponse =
  Message<"api.users.v1.SendSignupEmailVerificationCodeResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.SendSignupEmailVerificationCodeResponse.
 * Use `create(SendSignupEmailVerificationCodeResponseSchema)` to create a new message.
 */
export const SendSignupEmailVerificationCodeResponseSchema: GenMessage<SendSignupEmailVerificationCodeResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 7);

/**
 * @generated from message api.users.v1.VerifySignupEmailRequest
 */
export type VerifySignupEmailRequest =
  Message<"api.users.v1.VerifySignupEmailRequest"> & {
    /**
     * @generated from field: string email = 1;
     */
    email: string;

    /**
     * @generated from field: string verification_code = 2;
     */
    verificationCode: string;
  };

/**
 * Describes the message api.users.v1.VerifySignupEmailRequest.
 * Use `create(VerifySignupEmailRequestSchema)` to create a new message.
 */
export const VerifySignupEmailRequestSchema: GenMessage<VerifySignupEmailRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 8);

/**
 * @generated from message api.users.v1.VerifySignupEmailResponse
 */
export type VerifySignupEmailResponse =
  Message<"api.users.v1.VerifySignupEmailResponse"> & {
    /**
     * @generated from field: string token = 1;
     */
    token: string;
  };

/**
 * Describes the message api.users.v1.VerifySignupEmailResponse.
 * Use `create(VerifySignupEmailResponseSchema)` to create a new message.
 */
export const VerifySignupEmailResponseSchema: GenMessage<VerifySignupEmailResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 9);

/**
 * @generated from message api.users.v1.SignupWithEmailRequest
 */
export type SignupWithEmailRequest =
  Message<"api.users.v1.SignupWithEmailRequest"> & {
    /**
     * @generated from field: string full_name = 1;
     */
    fullName: string;

    /**
     * @generated from field: string password = 2;
     */
    password: string;

    /**
     * @generated from field: optional google.protobuf.Timestamp date_of_birth = 3;
     */
    dateOfBirth?: Timestamp;

    /**
     * @generated from field: string token = 4;
     */
    token: string;
  };

/**
 * Describes the message api.users.v1.SignupWithEmailRequest.
 * Use `create(SignupWithEmailRequestSchema)` to create a new message.
 */
export const SignupWithEmailRequestSchema: GenMessage<SignupWithEmailRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 10);

/**
 * @generated from message api.users.v1.SignupWithEmailResponse
 */
export type SignupWithEmailResponse =
  Message<"api.users.v1.SignupWithEmailResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.SignupWithEmailResponse.
 * Use `create(SignupWithEmailResponseSchema)` to create a new message.
 */
export const SignupWithEmailResponseSchema: GenMessage<SignupWithEmailResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 11);

/**
 * @generated from message api.users.v1.SignInWithEmailRequest
 */
export type SignInWithEmailRequest =
  Message<"api.users.v1.SignInWithEmailRequest"> & {
    /**
     * @generated from field: string email = 1;
     */
    email: string;

    /**
     * @generated from field: string password = 2;
     */
    password: string;
  };

/**
 * Describes the message api.users.v1.SignInWithEmailRequest.
 * Use `create(SignInWithEmailRequestSchema)` to create a new message.
 */
export const SignInWithEmailRequestSchema: GenMessage<SignInWithEmailRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 12);

/**
 * @generated from message api.users.v1.SignInWithEmailResponse
 */
export type SignInWithEmailResponse =
  Message<"api.users.v1.SignInWithEmailResponse"> & {
    /**
     * @generated from field: string access_token = 1;
     */
    accessToken: string;

    /**
     * @generated from field: string refresh_token = 2;
     */
    refreshToken: string;
  };

/**
 * Describes the message api.users.v1.SignInWithEmailResponse.
 * Use `create(SignInWithEmailResponseSchema)` to create a new message.
 */
export const SignInWithEmailResponseSchema: GenMessage<SignInWithEmailResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 13);

/**
 * @generated from message api.users.v1.SendForgotPasswordEmailRequest
 */
export type SendForgotPasswordEmailRequest =
  Message<"api.users.v1.SendForgotPasswordEmailRequest"> & {
    /**
     * @generated from field: string email = 1;
     */
    email: string;
  };

/**
 * Describes the message api.users.v1.SendForgotPasswordEmailRequest.
 * Use `create(SendForgotPasswordEmailRequestSchema)` to create a new message.
 */
export const SendForgotPasswordEmailRequestSchema: GenMessage<SendForgotPasswordEmailRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 14);

/**
 * @generated from message api.users.v1.SendForgotPasswordEmailResponse
 */
export type SendForgotPasswordEmailResponse =
  Message<"api.users.v1.SendForgotPasswordEmailResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.SendForgotPasswordEmailResponse.
 * Use `create(SendForgotPasswordEmailResponseSchema)` to create a new message.
 */
export const SendForgotPasswordEmailResponseSchema: GenMessage<SendForgotPasswordEmailResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 15);

/**
 * @generated from message api.users.v1.ResetPasswordRequest
 */
export type ResetPasswordRequest =
  Message<"api.users.v1.ResetPasswordRequest"> & {
    /**
     * @generated from field: string email = 1;
     */
    email: string;

    /**
     * @generated from field: string verification_code = 2;
     */
    verificationCode: string;

    /**
     * @generated from field: string new_password = 3;
     */
    newPassword: string;
  };

/**
 * Describes the message api.users.v1.ResetPasswordRequest.
 * Use `create(ResetPasswordRequestSchema)` to create a new message.
 */
export const ResetPasswordRequestSchema: GenMessage<ResetPasswordRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 16);

/**
 * @generated from message api.users.v1.ResetPasswordResponse
 */
export type ResetPasswordResponse =
  Message<"api.users.v1.ResetPasswordResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.ResetPasswordResponse.
 * Use `create(ResetPasswordResponseSchema)` to create a new message.
 */
export const ResetPasswordResponseSchema: GenMessage<ResetPasswordResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 17);

/**
 * @generated from message api.users.v1.DeleteProfileImageRequest
 */
export type DeleteProfileImageRequest =
  Message<"api.users.v1.DeleteProfileImageRequest"> & {};

/**
 * Describes the message api.users.v1.DeleteProfileImageRequest.
 * Use `create(DeleteProfileImageRequestSchema)` to create a new message.
 */
export const DeleteProfileImageRequestSchema: GenMessage<DeleteProfileImageRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 18);

/**
 * @generated from message api.users.v1.ChangePasswordRequest
 */
export type ChangePasswordRequest =
  Message<"api.users.v1.ChangePasswordRequest"> & {
    /**
     * @generated from field: string old_password = 1;
     */
    oldPassword: string;

    /**
     * @generated from field: string new_password = 2;
     */
    newPassword: string;
  };

/**
 * Describes the message api.users.v1.ChangePasswordRequest.
 * Use `create(ChangePasswordRequestSchema)` to create a new message.
 */
export const ChangePasswordRequestSchema: GenMessage<ChangePasswordRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 19);

/**
 * @generated from message api.users.v1.ChangePasswordResponse
 */
export type ChangePasswordResponse =
  Message<"api.users.v1.ChangePasswordResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.ChangePasswordResponse.
 * Use `create(ChangePasswordResponseSchema)` to create a new message.
 */
export const ChangePasswordResponseSchema: GenMessage<ChangePasswordResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 20);

/**
 * @generated from message api.users.v1.ChangeEmailVerificationRequest
 */
export type ChangeEmailVerificationRequest =
  Message<"api.users.v1.ChangeEmailVerificationRequest"> & {};

/**
 * Describes the message api.users.v1.ChangeEmailVerificationRequest.
 * Use `create(ChangeEmailVerificationRequestSchema)` to create a new message.
 */
export const ChangeEmailVerificationRequestSchema: GenMessage<ChangeEmailVerificationRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 21);

/**
 * @generated from message api.users.v1.ChangeEmailVerificationResponse
 */
export type ChangeEmailVerificationResponse =
  Message<"api.users.v1.ChangeEmailVerificationResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.ChangeEmailVerificationResponse.
 * Use `create(ChangeEmailVerificationResponseSchema)` to create a new message.
 */
export const ChangeEmailVerificationResponseSchema: GenMessage<ChangeEmailVerificationResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 22);

/**
 * @generated from message api.users.v1.VerifyChangeEmailRequest
 */
export type VerifyChangeEmailRequest =
  Message<"api.users.v1.VerifyChangeEmailRequest"> & {
    /**
     * @generated from field: string verification_code = 1;
     */
    verificationCode: string;
  };

/**
 * Describes the message api.users.v1.VerifyChangeEmailRequest.
 * Use `create(VerifyChangeEmailRequestSchema)` to create a new message.
 */
export const VerifyChangeEmailRequestSchema: GenMessage<VerifyChangeEmailRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 23);

/**
 * @generated from message api.users.v1.VerifyChangeEmailResponse
 */
export type VerifyChangeEmailResponse =
  Message<"api.users.v1.VerifyChangeEmailResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string token = 2;
     */
    token: string;
  };

/**
 * Describes the message api.users.v1.VerifyChangeEmailResponse.
 * Use `create(VerifyChangeEmailResponseSchema)` to create a new message.
 */
export const VerifyChangeEmailResponseSchema: GenMessage<VerifyChangeEmailResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 24);

/**
 * @generated from message api.users.v1.SendVerifyNewEmailRequest
 */
export type SendVerifyNewEmailRequest =
  Message<"api.users.v1.SendVerifyNewEmailRequest"> & {
    /**
     * @generated from field: string new_email = 2;
     */
    newEmail: string;

    /**
     * @generated from field: string token = 3;
     */
    token: string;
  };

/**
 * Describes the message api.users.v1.SendVerifyNewEmailRequest.
 * Use `create(SendVerifyNewEmailRequestSchema)` to create a new message.
 */
export const SendVerifyNewEmailRequestSchema: GenMessage<SendVerifyNewEmailRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 25);

/**
 * @generated from message api.users.v1.SendVerifyNewEmailResponse
 */
export type SendVerifyNewEmailResponse =
  Message<"api.users.v1.SendVerifyNewEmailResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.SendVerifyNewEmailResponse.
 * Use `create(SendVerifyNewEmailResponseSchema)` to create a new message.
 */
export const SendVerifyNewEmailResponseSchema: GenMessage<SendVerifyNewEmailResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 26);

/**
 * @generated from message api.users.v1.VerifyNewEmailWithCodeRequest
 */
export type VerifyNewEmailWithCodeRequest =
  Message<"api.users.v1.VerifyNewEmailWithCodeRequest"> & {
    /**
     * @generated from field: string verification_code = 1;
     */
    verificationCode: string;

    /**
     * @generated from field: string new_email = 2;
     */
    newEmail: string;
  };

/**
 * Describes the message api.users.v1.VerifyNewEmailWithCodeRequest.
 * Use `create(VerifyNewEmailWithCodeRequestSchema)` to create a new message.
 */
export const VerifyNewEmailWithCodeRequestSchema: GenMessage<VerifyNewEmailWithCodeRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 27);

/**
 * @generated from message api.users.v1.VerifyNewEmailRequest
 */
export type VerifyNewEmailRequest =
  Message<"api.users.v1.VerifyNewEmailRequest"> & {
    /**
     * @generated from field: optional api.users.v1.VerifyNewEmailWithCodeRequest verify_new_email_with_code = 1;
     */
    verifyNewEmailWithCode?: VerifyNewEmailWithCodeRequest;

    /**
     * @generated from field: optional string token = 2;
     */
    token?: string;
  };

/**
 * Describes the message api.users.v1.VerifyNewEmailRequest.
 * Use `create(VerifyNewEmailRequestSchema)` to create a new message.
 */
export const VerifyNewEmailRequestSchema: GenMessage<VerifyNewEmailRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 28);

/**
 * @generated from message api.users.v1.VerifyNewEmailResponse
 */
export type VerifyNewEmailResponse =
  Message<"api.users.v1.VerifyNewEmailResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.VerifyNewEmailResponse.
 * Use `create(VerifyNewEmailResponseSchema)` to create a new message.
 */
export const VerifyNewEmailResponseSchema: GenMessage<VerifyNewEmailResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 29);

/**
 * @generated from message api.users.v1.UpdateUserDetailsRequest
 */
export type UpdateUserDetailsRequest =
  Message<"api.users.v1.UpdateUserDetailsRequest"> & {
    /**
     * @generated from field: string full_name = 1;
     */
    fullName: string;

    /**
     * @generated from field: optional google.protobuf.Timestamp date_of_birth = 2;
     */
    dateOfBirth?: Timestamp;
  };

/**
 * Describes the message api.users.v1.UpdateUserDetailsRequest.
 * Use `create(UpdateUserDetailsRequestSchema)` to create a new message.
 */
export const UpdateUserDetailsRequestSchema: GenMessage<UpdateUserDetailsRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 30);

/**
 * @generated from message api.users.v1.UpdateUserDetailsResponse
 */
export type UpdateUserDetailsResponse =
  Message<"api.users.v1.UpdateUserDetailsResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.UpdateUserDetailsResponse.
 * Use `create(UpdateUserDetailsResponseSchema)` to create a new message.
 */
export const UpdateUserDetailsResponseSchema: GenMessage<UpdateUserDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 31);

/**
 * @generated from message api.users.v1.UpdateUserImageRequest
 */
export type UpdateUserImageRequest =
  Message<"api.users.v1.UpdateUserImageRequest"> & {
    /**
     * @generated from field: string image = 1;
     */
    image: string;
  };

/**
 * Describes the message api.users.v1.UpdateUserImageRequest.
 * Use `create(UpdateUserImageRequestSchema)` to create a new message.
 */
export const UpdateUserImageRequestSchema: GenMessage<UpdateUserImageRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 32);

/**
 * @generated from message api.users.v1.UpdateUserImageResponse
 */
export type UpdateUserImageResponse =
  Message<"api.users.v1.UpdateUserImageResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.UpdateUserImageResponse.
 * Use `create(UpdateUserImageResponseSchema)` to create a new message.
 */
export const UpdateUserImageResponseSchema: GenMessage<UpdateUserImageResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 33);

/**
 * @generated from message api.users.v1.ListSessionsRequest
 */
export type ListSessionsRequest =
  Message<"api.users.v1.ListSessionsRequest"> & {};

/**
 * Describes the message api.users.v1.ListSessionsRequest.
 * Use `create(ListSessionsRequestSchema)` to create a new message.
 */
export const ListSessionsRequestSchema: GenMessage<ListSessionsRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 34);

/**
 * @generated from message api.users.v1.Session
 */
export type Session = Message<"api.users.v1.Session"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string user_agent = 2;
   */
  userAgent: string;

  /**
   * @generated from field: string ip_address = 3;
   */
  ipAddress: string;

  /**
   * @generated from field: string created_at = 4;
   */
  createdAt: string;

  /**
   * @generated from field: bool isCurrent = 5;
   */
  isCurrent: boolean;
};

/**
 * Describes the message api.users.v1.Session.
 * Use `create(SessionSchema)` to create a new message.
 */
export const SessionSchema: GenMessage<Session> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 35);

/**
 * @generated from message api.users.v1.ListSessionsResponse
 */
export type ListSessionsResponse =
  Message<"api.users.v1.ListSessionsResponse"> & {
    /**
     * @generated from field: repeated api.users.v1.Session sessions = 1;
     */
    sessions: Session[];
  };

/**
 * Describes the message api.users.v1.ListSessionsResponse.
 * Use `create(ListSessionsResponseSchema)` to create a new message.
 */
export const ListSessionsResponseSchema: GenMessage<ListSessionsResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 36);

/**
 * @generated from message api.users.v1.RevokeSessionsRequest
 */
export type RevokeSessionsRequest =
  Message<"api.users.v1.RevokeSessionsRequest"> & {
    /**
     * @generated from field: repeated string session_ids = 1;
     */
    sessionIds: string[];
  };

/**
 * Describes the message api.users.v1.RevokeSessionsRequest.
 * Use `create(RevokeSessionsRequestSchema)` to create a new message.
 */
export const RevokeSessionsRequestSchema: GenMessage<RevokeSessionsRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 37);

/**
 * @generated from message api.users.v1.RevokeSessionsResponse
 */
export type RevokeSessionsResponse =
  Message<"api.users.v1.RevokeSessionsResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.RevokeSessionsResponse.
 * Use `create(RevokeSessionsResponseSchema)` to create a new message.
 */
export const RevokeSessionsResponseSchema: GenMessage<RevokeSessionsResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 38);

/**
 * @generated from message api.users.v1.RevokeOtherSessionsRequest
 */
export type RevokeOtherSessionsRequest =
  Message<"api.users.v1.RevokeOtherSessionsRequest"> & {};

/**
 * Describes the message api.users.v1.RevokeOtherSessionsRequest.
 * Use `create(RevokeOtherSessionsRequestSchema)` to create a new message.
 */
export const RevokeOtherSessionsRequestSchema: GenMessage<RevokeOtherSessionsRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 39);

/**
 * @generated from message api.users.v1.RevokeOtherSessionsResponse
 */
export type RevokeOtherSessionsResponse =
  Message<"api.users.v1.RevokeOtherSessionsResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.RevokeOtherSessionsResponse.
 * Use `create(RevokeOtherSessionsResponseSchema)` to create a new message.
 */
export const RevokeOtherSessionsResponseSchema: GenMessage<RevokeOtherSessionsResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 40);

/**
 * @generated from message api.users.v1.LinkSocialRequest
 */
export type LinkSocialRequest = Message<"api.users.v1.LinkSocialRequest"> & {
  /**
   * @generated from field: string provider = 1;
   */
  provider: string;

  /**
   * @generated from field: string code = 2;
   */
  code: string;
};

/**
 * Describes the message api.users.v1.LinkSocialRequest.
 * Use `create(LinkSocialRequestSchema)` to create a new message.
 */
export const LinkSocialRequestSchema: GenMessage<LinkSocialRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 41);

/**
 * @generated from message api.users.v1.LinkSocialResponse
 */
export type LinkSocialResponse = Message<"api.users.v1.LinkSocialResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.LinkSocialResponse.
 * Use `create(LinkSocialResponseSchema)` to create a new message.
 */
export const LinkSocialResponseSchema: GenMessage<LinkSocialResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 42);

/**
 * @generated from message api.users.v1.ListAccountsRequest
 */
export type ListAccountsRequest =
  Message<"api.users.v1.ListAccountsRequest"> & {};

/**
 * Describes the message api.users.v1.ListAccountsRequest.
 * Use `create(ListAccountsRequestSchema)` to create a new message.
 */
export const ListAccountsRequestSchema: GenMessage<ListAccountsRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 43);

/**
 * @generated from message api.users.v1.ListAccountsResponse
 */
export type ListAccountsResponse =
  Message<"api.users.v1.ListAccountsResponse"> & {
    /**
     * @generated from field: repeated api.users.v1.Account accounts = 1;
     */
    accounts: Account[];
  };

/**
 * Describes the message api.users.v1.ListAccountsResponse.
 * Use `create(ListAccountsResponseSchema)` to create a new message.
 */
export const ListAccountsResponseSchema: GenMessage<ListAccountsResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 44);

/**
 * @generated from message api.users.v1.Account
 */
export type Account = Message<"api.users.v1.Account"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string provider = 2;
   */
  provider: string;

  /**
   * @generated from field: string user_id = 3;
   */
  userId: string;
};

/**
 * Describes the message api.users.v1.Account.
 * Use `create(AccountSchema)` to create a new message.
 */
export const AccountSchema: GenMessage<Account> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 45);

/**
 * @generated from message api.users.v1.UnlinkAccountRequest
 */
export type UnlinkAccountRequest =
  Message<"api.users.v1.UnlinkAccountRequest"> & {
    /**
     * @generated from field: string account_id = 1;
     */
    accountId: string;
  };

/**
 * Describes the message api.users.v1.UnlinkAccountRequest.
 * Use `create(UnlinkAccountRequestSchema)` to create a new message.
 */
export const UnlinkAccountRequestSchema: GenMessage<UnlinkAccountRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 46);

/**
 * @generated from message api.users.v1.UnlinkAccountResponse
 */
export type UnlinkAccountResponse =
  Message<"api.users.v1.UnlinkAccountResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.users.v1.UnlinkAccountResponse.
 * Use `create(UnlinkAccountResponseSchema)` to create a new message.
 */
export const UnlinkAccountResponseSchema: GenMessage<UnlinkAccountResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 47);

/**
 * @generated from message api.users.v1.RefreshTokenRequest
 */
export type RefreshTokenRequest =
  Message<"api.users.v1.RefreshTokenRequest"> & {
    /**
     * @generated from field: string refresh_token = 1;
     */
    refreshToken: string;
  };

/**
 * Describes the message api.users.v1.RefreshTokenRequest.
 * Use `create(RefreshTokenRequestSchema)` to create a new message.
 */
export const RefreshTokenRequestSchema: GenMessage<RefreshTokenRequest> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 48);

/**
 * @generated from message api.users.v1.RefreshTokenResponse
 */
export type RefreshTokenResponse =
  Message<"api.users.v1.RefreshTokenResponse"> & {
    /**
     * @generated from field: string access_token = 1;
     */
    accessToken: string;

    /**
     * @generated from field: string refresh_token = 2;
     */
    refreshToken: string;
  };

/**
 * Describes the message api.users.v1.RefreshTokenResponse.
 * Use `create(RefreshTokenResponseSchema)` to create a new message.
 */
export const RefreshTokenResponseSchema: GenMessage<RefreshTokenResponse> =
  /*@__PURE__*/
  messageDesc(file_users_v1_auth, 49);

/**
 * @generated from service api.users.v1.AuthService
 */
export const AuthService: GenService<{
  /**
   * @generated from rpc api.users.v1.AuthService.SignInWithEmail
   */
  signInWithEmail: {
    methodKind: "unary";
    input: typeof SignInWithEmailRequestSchema;
    output: typeof SignInWithEmailResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.RefreshToken
   */
  refreshToken: {
    methodKind: "unary";
    input: typeof RefreshTokenRequestSchema;
    output: typeof RefreshTokenResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.SignInWithSocial
   */
  signInWithSocial: {
    methodKind: "unary";
    input: typeof SignInWithSocialRequestSchema;
    output: typeof SignInWithSocialResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.SignOut
   */
  signOut: {
    methodKind: "unary";
    input: typeof SignOutRequestSchema;
    output: typeof SignOutResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.GetSession
   */
  getSession: {
    methodKind: "unary";
    input: typeof GetSessionRequestSchema;
    output: typeof GetSessionResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.SendSignupEmailVerificationCode
   */
  sendSignupEmailVerificationCode: {
    methodKind: "unary";
    input: typeof SendSignupEmailVerificationCodeRequestSchema;
    output: typeof SendSignupEmailVerificationCodeResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.VerifySignupEmail
   */
  verifySignupEmail: {
    methodKind: "unary";
    input: typeof VerifySignupEmailRequestSchema;
    output: typeof VerifySignupEmailResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.SignupWithEmail
   */
  signupWithEmail: {
    methodKind: "unary";
    input: typeof SignupWithEmailRequestSchema;
    output: typeof SignupWithEmailResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.SendForgotPasswordEmail
   */
  sendForgotPasswordEmail: {
    methodKind: "unary";
    input: typeof SendForgotPasswordEmailRequestSchema;
    output: typeof SendForgotPasswordEmailResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.ResetPassword
   */
  resetPassword: {
    methodKind: "unary";
    input: typeof ResetPasswordRequestSchema;
    output: typeof ResetPasswordResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.ChangePassword
   */
  changePassword: {
    methodKind: "unary";
    input: typeof ChangePasswordRequestSchema;
    output: typeof ChangePasswordResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.ChangeEmailVerification
   */
  changeEmailVerification: {
    methodKind: "unary";
    input: typeof ChangeEmailVerificationRequestSchema;
    output: typeof ChangeEmailVerificationResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.VerifyChangeEmail
   */
  verifyChangeEmail: {
    methodKind: "unary";
    input: typeof VerifyChangeEmailRequestSchema;
    output: typeof VerifyChangeEmailResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.SendVerifyNewEmail
   */
  sendVerifyNewEmail: {
    methodKind: "unary";
    input: typeof SendVerifyNewEmailRequestSchema;
    output: typeof SendVerifyNewEmailResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.VerifyNewEmail
   */
  verifyNewEmail: {
    methodKind: "unary";
    input: typeof VerifyNewEmailRequestSchema;
    output: typeof VerifyNewEmailResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.UpdateUserDetails
   */
  updateUserDetails: {
    methodKind: "unary";
    input: typeof UpdateUserDetailsRequestSchema;
    output: typeof UpdateUserDetailsResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.UpdateUserImage
   */
  updateUserImage: {
    methodKind: "unary";
    input: typeof UpdateUserImageRequestSchema;
    output: typeof UpdateUserImageResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.ListSessions
   */
  listSessions: {
    methodKind: "unary";
    input: typeof ListSessionsRequestSchema;
    output: typeof ListSessionsResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.RevokeSessions
   */
  revokeSessions: {
    methodKind: "unary";
    input: typeof RevokeSessionsRequestSchema;
    output: typeof RevokeSessionsResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.RevokeOtherSessions
   */
  revokeOtherSessions: {
    methodKind: "unary";
    input: typeof RevokeOtherSessionsRequestSchema;
    output: typeof RevokeOtherSessionsResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.LinkSocial
   */
  linkSocial: {
    methodKind: "unary";
    input: typeof LinkSocialRequestSchema;
    output: typeof LinkSocialResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.ListAccounts
   */
  listAccounts: {
    methodKind: "unary";
    input: typeof ListAccountsRequestSchema;
    output: typeof ListAccountsResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.UnlinkAccount
   */
  unlinkAccount: {
    methodKind: "unary";
    input: typeof UnlinkAccountRequestSchema;
    output: typeof UnlinkAccountResponseSchema;
  };
  /**
   * @generated from rpc api.users.v1.AuthService.DeleteProfileImage
   */
  deleteProfileImage: {
    methodKind: "unary";
    input: typeof DeleteProfileImageRequestSchema;
    output: typeof UnlinkAccountResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_users_v1_auth, 0);
