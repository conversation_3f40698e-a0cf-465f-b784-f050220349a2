// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file shared/v1/generic.proto (package api.shared.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";

/**
 * Describes the file shared/v1/generic.proto.
 */
export const file_shared_v1_generic: GenFile =
  /*@__PURE__*/
  fileDesc(
    "ChdzaGFyZWQvdjEvZ2VuZXJpYy5wcm90bxINYXBpLnNoYXJlZC52MSJDCg9HZW5lcmljUmVzcG9uc2USDgoGc3RhdHVzGAEgASgFEg8KB21lc3NhZ2UYAiABKAkSDwoHc3VjY2VzcxgDIAEoCEIyWjBnaXRodWIuY29tL25zcC1pbmMvdnR1YmVyL2FwaS9zaGFyZWQvdjE7c2hhcmVkdjFiBnByb3RvMw",
  );

/**
 * @generated from message api.shared.v1.GenericResponse
 */
export type GenericResponse = Message<"api.shared.v1.GenericResponse"> & {
  /**
   * @generated from field: int32 status = 1;
   */
  status: number;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  /**
   * @generated from field: bool success = 3;
   */
  success: boolean;
};

/**
 * Describes the message api.shared.v1.GenericResponse.
 * Use `create(GenericResponseSchema)` to create a new message.
 */
export const GenericResponseSchema: GenMessage<GenericResponse> =
  /*@__PURE__*/
  messageDesc(file_shared_v1_generic, 0);
