// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file shared/v1/profile.proto (package api.shared.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { SocialMediaLinks } from "./social_media_links_pb";
import { file_shared_v1_social_media_links } from "./social_media_links_pb";

/**
 * Describes the file shared/v1/profile.proto.
 */
export const file_shared_v1_profile: GenFile =
  /*@__PURE__*/
  fileDesc(
    "ChdzaGFyZWQvdjEvcHJvZmlsZS5wcm90bxINYXBpLnNoYXJlZC52MSKOAgoHUHJvZmlsZRIKCgJpZBgBIAEoCRIMCgRuYW1lGAIgASgJEhIKBWltYWdlGAMgASgJSACIAQESFQoIZnVyaWdhbmEYBCABKAlIAYgBARIZCgxpbnRyb2R1Y3Rpb24YBSABKAlIAogBARJAChJzb2NpYWxfbWVkaWFfbGlua3MYBiABKAsyHy5hcGkuc2hhcmVkLnYxLlNvY2lhbE1lZGlhTGlua3NIA4gBARIVCgh1c2VybmFtZRgHIAEoCUgEiAEBQggKBl9pbWFnZUILCglfZnVyaWdhbmFCDwoNX2ludHJvZHVjdGlvbkIVChNfc29jaWFsX21lZGlhX2xpbmtzQgsKCV91c2VybmFtZUIyWjBnaXRodWIuY29tL25zcC1pbmMvdnR1YmVyL2FwaS9zaGFyZWQvdjE7c2hhcmVkdjFiBnByb3RvMw",
    [file_shared_v1_social_media_links],
  );

/**
 * @generated from message api.shared.v1.Profile
 */
export type Profile = Message<"api.shared.v1.Profile"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: optional string image = 3;
   */
  image?: string;

  /**
   * @generated from field: optional string furigana = 4;
   */
  furigana?: string;

  /**
   * @generated from field: optional string introduction = 5;
   */
  introduction?: string;

  /**
   * @generated from field: optional api.shared.v1.SocialMediaLinks social_media_links = 6;
   */
  socialMediaLinks?: SocialMediaLinks;

  /**
   * @generated from field: optional string username = 7;
   */
  username?: string;
};

/**
 * Describes the message api.shared.v1.Profile.
 * Use `create(ProfileSchema)` to create a new message.
 */
export const ProfileSchema: GenMessage<Profile> =
  /*@__PURE__*/
  messageDesc(file_shared_v1_profile, 0);
