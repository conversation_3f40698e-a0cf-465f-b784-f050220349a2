// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file shared/v1/social_media_links.proto (package api.shared.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";

/**
 * Describes the file shared/v1/social_media_links.proto.
 */
export const file_shared_v1_social_media_links: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiJzaGFyZWQvdjEvc29jaWFsX21lZGlhX2xpbmtzLnByb3RvEg1hcGkuc2hhcmVkLnYxIt4BChBTb2NpYWxNZWRpYUxpbmtzEhQKB3R3aXR0ZXIYASABKAlIAIgBARIUCgd5b3V0dWJlGAIgASgJSAGIAQESEwoGdHdpdGNoGAMgASgJSAKIAQESEwoGdGlrdG9rGAQgASgJSAOIAQESFgoJaW5zdGFncmFtGAUgASgJSASIAQESFAoHZGlzY29yZBgGIAEoCUgFiAEBQgoKCF90d2l0dGVyQgoKCF95b3V0dWJlQgkKB190d2l0Y2hCCQoHX3Rpa3Rva0IMCgpfaW5zdGFncmFtQgoKCF9kaXNjb3JkQjJaMGdpdGh1Yi5jb20vbnNwLWluYy92dHViZXIvYXBpL3NoYXJlZC92MTtzaGFyZWR2MWIGcHJvdG8z",
  );

/**
 * @generated from message api.shared.v1.SocialMediaLinks
 */
export type SocialMediaLinks = Message<"api.shared.v1.SocialMediaLinks"> & {
  /**
   * @gotag: validate:"omitempty,url"
   *
   * @generated from field: optional string twitter = 1;
   */
  twitter?: string;

  /**
   * @gotag: validate:"omitempty,url"
   *
   * @generated from field: optional string youtube = 2;
   */
  youtube?: string;

  /**
   * @gotag: validate:"omitempty,url"
   *
   * @generated from field: optional string twitch = 3;
   */
  twitch?: string;

  /**
   * @gotag: validate:"omitempty,url"
   *
   * @generated from field: optional string tiktok = 4;
   */
  tiktok?: string;

  /**
   * @gotag: validate:"omitempty,url"
   *
   * @generated from field: optional string instagram = 5;
   */
  instagram?: string;

  /**
   * @gotag: validate:"omitempty,url"
   *
   * @generated from field: optional string discord = 6;
   */
  discord?: string;
};

/**
 * Describes the message api.shared.v1.SocialMediaLinks.
 * Use `create(SocialMediaLinksSchema)` to create a new message.
 */
export const SocialMediaLinksSchema: GenMessage<SocialMediaLinks> =
  /*@__PURE__*/
  messageDesc(file_shared_v1_social_media_links, 0);
