// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file shared/v1/errors.proto (package api.shared.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";

/**
 * Describes the file shared/v1/errors.proto.
 */
export const file_shared_v1_errors: GenFile =
  /*@__PURE__*/
  fileDesc(
    "ChZzaGFyZWQvdjEvZXJyb3JzLnByb3RvEg1hcGkuc2hhcmVkLnYxIjgKFlZhbGlkYXRpb25FcnJvckRldGFpbHMSDQoFZmllbGQYASABKAkSDwoHbWVzc2FnZRgCIAEoCSJJCg9WYWxpZGF0aW9uRXJyb3ISNgoHZGV0YWlscxgBIAMoCzIlLmFwaS5zaGFyZWQudjEuVmFsaWRhdGlvbkVycm9yRGV0YWlsc0IyWjBnaXRodWIuY29tL25zcC1pbmMvdnR1YmVyL2FwaS9zaGFyZWQvdjE7c2hhcmVkdjFiBnByb3RvMw",
  );

/**
 * @generated from message api.shared.v1.ValidationErrorDetails
 */
export type ValidationErrorDetails =
  Message<"api.shared.v1.ValidationErrorDetails"> & {
    /**
     * @generated from field: string field = 1;
     */
    field: string;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.shared.v1.ValidationErrorDetails.
 * Use `create(ValidationErrorDetailsSchema)` to create a new message.
 */
export const ValidationErrorDetailsSchema: GenMessage<ValidationErrorDetails> =
  /*@__PURE__*/
  messageDesc(file_shared_v1_errors, 0);

/**
 * @generated from message api.shared.v1.ValidationError
 */
export type ValidationError = Message<"api.shared.v1.ValidationError"> & {
  /**
   * @generated from field: repeated api.shared.v1.ValidationErrorDetails details = 1;
   */
  details: ValidationErrorDetails[];
};

/**
 * Describes the message api.shared.v1.ValidationError.
 * Use `create(ValidationErrorSchema)` to create a new message.
 */
export const ValidationErrorSchema: GenMessage<ValidationError> =
  /*@__PURE__*/
  messageDesc(file_shared_v1_errors, 1);
