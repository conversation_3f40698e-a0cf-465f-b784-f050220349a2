// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file vtubers/v1/vtuberprofiles.proto (package api.vtubers.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type {
  PaginationDetails,
  PaginationRequest,
} from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import type { SocialMediaLinks } from "../../shared/v1/social_media_links_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file vtubers/v1/vtuberprofiles.proto.
 */
export const file_vtubers_v1_vtuberprofiles: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.vtubers.v1.AddVtuberProfileRequest
 */
export type AddVtuberProfileRequest =
  Message<"api.vtubers.v1.AddVtuberProfileRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string display_name = 1;
     */
    displayName: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string furigana = 2;
     */
    furigana: string;

    /**
     * @generated from field: string image = 3;
     */
    image: string;

    /**
     * @generated from field: string banner_image = 4;
     */
    bannerImage: string;

    /**
     * @generated from field: optional string description = 5;
     */
    description?: string;

    /**
     * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 6;
     */
    socialMediaLinks?: SocialMediaLinks;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string username = 7;
     */
    username: string;

    /**
     * @generated from field: repeated string categories = 8;
     */
    categories: string[];
  };

/**
 * Describes the message api.vtubers.v1.AddVtuberProfileRequest.
 * Use `create(AddVtuberProfileRequestSchema)` to create a new message.
 */
export const AddVtuberProfileRequestSchema: GenMessage<AddVtuberProfileRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 0);

/**
 * @generated from message api.vtubers.v1.AddVtuberProfileResponse
 */
export type AddVtuberProfileResponse =
  Message<"api.vtubers.v1.AddVtuberProfileResponse"> & {
    /**
     * @generated from field: api.vtubers.v1.VtuberProfile data = 1;
     */
    data?: VtuberProfile;
  };

/**
 * Describes the message api.vtubers.v1.AddVtuberProfileResponse.
 * Use `create(AddVtuberProfileResponseSchema)` to create a new message.
 */
export const AddVtuberProfileResponseSchema: GenMessage<AddVtuberProfileResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 1);

/**
 * @generated from message api.vtubers.v1.VtuberProfile
 */
export type VtuberProfile = Message<"api.vtubers.v1.VtuberProfile"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * @generated from field: string display_name = 3;
   */
  displayName: string;

  /**
   * @generated from field: string furigana = 4;
   */
  furigana: string;

  /**
   * @generated from field: optional string image = 5;
   */
  image?: string;

  /**
   * @generated from field: optional string banner_image = 6;
   */
  bannerImage?: string;

  /**
   * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 7;
   */
  socialMediaLinks?: SocialMediaLinks;

  /**
   * @generated from field: optional string description = 8;
   */
  description?: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 9;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: bool is_user_subscribed = 10;
   */
  isUserSubscribed: boolean;

  /**
   * @generated from field: bool has_liked = 11;
   */
  hasLiked: boolean;

  /**
   * @generated from field: string username = 12;
   */
  username: string;

  /**
   * @generated from field: repeated string categories = 13;
   */
  categories: string[];

  /**
   * @generated from field: bool is_plan_active = 14;
   */
  isPlanActive: boolean;
};

/**
 * Describes the message api.vtubers.v1.VtuberProfile.
 * Use `create(VtuberProfileSchema)` to create a new message.
 */
export const VtuberProfileSchema: GenMessage<VtuberProfile> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 2);

/**
 * @generated from message api.vtubers.v1.GetVtuberProfileByIdRequest
 */
export type GetVtuberProfileByIdRequest =
  Message<"api.vtubers.v1.GetVtuberProfileByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;
  };

/**
 * Describes the message api.vtubers.v1.GetVtuberProfileByIdRequest.
 * Use `create(GetVtuberProfileByIdRequestSchema)` to create a new message.
 */
export const GetVtuberProfileByIdRequestSchema: GenMessage<GetVtuberProfileByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 3);

/**
 * @generated from message api.vtubers.v1.GetVtuberProfileRequest
 */
export type GetVtuberProfileRequest =
  Message<"api.vtubers.v1.GetVtuberProfileRequest"> & {};

/**
 * Describes the message api.vtubers.v1.GetVtuberProfileRequest.
 * Use `create(GetVtuberProfileRequestSchema)` to create a new message.
 */
export const GetVtuberProfileRequestSchema: GenMessage<GetVtuberProfileRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 4);

/**
 * @generated from message api.vtubers.v1.GetVtuberProfileByIdResponse
 */
export type GetVtuberProfileByIdResponse =
  Message<"api.vtubers.v1.GetVtuberProfileByIdResponse"> & {
    /**
     * @generated from field: api.vtubers.v1.VtuberProfile data = 1;
     */
    data?: VtuberProfile;
  };

/**
 * Describes the message api.vtubers.v1.GetVtuberProfileByIdResponse.
 * Use `create(GetVtuberProfileByIdResponseSchema)` to create a new message.
 */
export const GetVtuberProfileByIdResponseSchema: GenMessage<GetVtuberProfileByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 5);

/**
 * @generated from message api.vtubers.v1.UpdateVtuberProfileRequest
 */
export type UpdateVtuberProfileRequest =
  Message<"api.vtubers.v1.UpdateVtuberProfileRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string display_name = 2;
     */
    displayName: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string furigana = 3;
     */
    furigana: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string image = 4;
     */
    image: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string banner_image = 5;
     */
    bannerImage: string;

    /**
     * @generated from field: optional string description = 6;
     */
    description?: string;

    /**
     * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 7;
     */
    socialMediaLinks?: SocialMediaLinks;

    /**
     * @generated from field: repeated string categories = 8;
     */
    categories: string[];

    /**
     * @generated from field: bool is_plan_active = 9;
     */
    isPlanActive: boolean;
  };

/**
 * Describes the message api.vtubers.v1.UpdateVtuberProfileRequest.
 * Use `create(UpdateVtuberProfileRequestSchema)` to create a new message.
 */
export const UpdateVtuberProfileRequestSchema: GenMessage<UpdateVtuberProfileRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 6);

/**
 * @generated from message api.vtubers.v1.GetAllVtuberProfilesRequest
 */
export type GetAllVtuberProfilesRequest =
  Message<"api.vtubers.v1.GetAllVtuberProfilesRequest"> & {
    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;

    /**
     * @generated from field: optional string display_name = 2;
     */
    displayName?: string;

    /**
     * @generated from field: optional string category_id = 3;
     */
    categoryId?: string;
  };

/**
 * Describes the message api.vtubers.v1.GetAllVtuberProfilesRequest.
 * Use `create(GetAllVtuberProfilesRequestSchema)` to create a new message.
 */
export const GetAllVtuberProfilesRequestSchema: GenMessage<GetAllVtuberProfilesRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 7);

/**
 * @generated from message api.vtubers.v1.SearchVtuberProfilesRequest
 */
export type SearchVtuberProfilesRequest =
  Message<"api.vtubers.v1.SearchVtuberProfilesRequest"> & {
    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string query = 2;
     */
    query: string;
  };

/**
 * Describes the message api.vtubers.v1.SearchVtuberProfilesRequest.
 * Use `create(SearchVtuberProfilesRequestSchema)` to create a new message.
 */
export const SearchVtuberProfilesRequestSchema: GenMessage<SearchVtuberProfilesRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 8);

/**
 * @generated from message api.vtubers.v1.GetAllVtuberProfilesResponse
 */
export type GetAllVtuberProfilesResponse =
  Message<"api.vtubers.v1.GetAllVtuberProfilesResponse"> & {
    /**
     * @generated from field: repeated api.vtubers.v1.VtuberProfile data = 1;
     */
    data: VtuberProfile[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.vtubers.v1.GetAllVtuberProfilesResponse.
 * Use `create(GetAllVtuberProfilesResponseSchema)` to create a new message.
 */
export const GetAllVtuberProfilesResponseSchema: GenMessage<GetAllVtuberProfilesResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 9);

/**
 * @generated from message api.vtubers.v1.DeleteVtuberProfileByIdRequest
 */
export type DeleteVtuberProfileByIdRequest =
  Message<"api.vtubers.v1.DeleteVtuberProfileByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;
  };

/**
 * Describes the message api.vtubers.v1.DeleteVtuberProfileByIdRequest.
 * Use `create(DeleteVtuberProfileByIdRequestSchema)` to create a new message.
 */
export const DeleteVtuberProfileByIdRequestSchema: GenMessage<DeleteVtuberProfileByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 10);

/**
 * @generated from message api.vtubers.v1.VerifyVtuberProfileRequest
 */
export type VerifyVtuberProfileRequest =
  Message<"api.vtubers.v1.VerifyVtuberProfileRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;
  };

/**
 * Describes the message api.vtubers.v1.VerifyVtuberProfileRequest.
 * Use `create(VerifyVtuberProfileRequestSchema)` to create a new message.
 */
export const VerifyVtuberProfileRequestSchema: GenMessage<VerifyVtuberProfileRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 11);

/**
 * @generated from message api.vtubers.v1.CreateVtuberProfileAccessRequest
 */
export type CreateVtuberProfileAccessRequest =
  Message<"api.vtubers.v1.CreateVtuberProfileAccessRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string description = 1;
     */
    description: string;
  };

/**
 * Describes the message api.vtubers.v1.CreateVtuberProfileAccessRequest.
 * Use `create(CreateVtuberProfileAccessRequestSchema)` to create a new message.
 */
export const CreateVtuberProfileAccessRequestSchema: GenMessage<CreateVtuberProfileAccessRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 12);

/**
 * @generated from message api.vtubers.v1.UpdateVtuberProfileAccessRequest
 */
export type UpdateVtuberProfileAccessRequest =
  Message<"api.vtubers.v1.UpdateVtuberProfileAccessRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string description = 1;
     */
    description: string;
  };

/**
 * Describes the message api.vtubers.v1.UpdateVtuberProfileAccessRequest.
 * Use `create(UpdateVtuberProfileAccessRequestSchema)` to create a new message.
 */
export const UpdateVtuberProfileAccessRequestSchema: GenMessage<UpdateVtuberProfileAccessRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 13);

/**
 * @generated from message api.vtubers.v1.UpdateVtuberProfileAccessResponse
 */
export type UpdateVtuberProfileAccessResponse =
  Message<"api.vtubers.v1.UpdateVtuberProfileAccessResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.vtubers.v1.UpdateVtuberProfileAccessResponse.
 * Use `create(UpdateVtuberProfileAccessResponseSchema)` to create a new message.
 */
export const UpdateVtuberProfileAccessResponseSchema: GenMessage<UpdateVtuberProfileAccessResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 14);

/**
 * @generated from message api.vtubers.v1.GetAllVtuberProfileAccessRequest
 */
export type GetAllVtuberProfileAccessRequest =
  Message<"api.vtubers.v1.GetAllVtuberProfileAccessRequest"> & {
    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;

    /**
     * @generated from field: optional string status = 2;
     */
    status?: string;
  };

/**
 * Describes the message api.vtubers.v1.GetAllVtuberProfileAccessRequest.
 * Use `create(GetAllVtuberProfileAccessRequestSchema)` to create a new message.
 */
export const GetAllVtuberProfileAccessRequestSchema: GenMessage<GetAllVtuberProfileAccessRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 15);

/**
 * @generated from message api.vtubers.v1.GetAllVtuberProfileAccessResponse
 */
export type GetAllVtuberProfileAccessResponse =
  Message<"api.vtubers.v1.GetAllVtuberProfileAccessResponse"> & {
    /**
     * @generated from field: repeated api.vtubers.v1.VtuberAccessRequest data = 1;
     */
    data: VtuberAccessRequest[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.vtubers.v1.GetAllVtuberProfileAccessResponse.
 * Use `create(GetAllVtuberProfileAccessResponseSchema)` to create a new message.
 */
export const GetAllVtuberProfileAccessResponseSchema: GenMessage<GetAllVtuberProfileAccessResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 16);

/**
 * @generated from message api.vtubers.v1.VtuberAccessRequest
 */
export type VtuberAccessRequest =
  Message<"api.vtubers.v1.VtuberAccessRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string status = 3;
     */
    status: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: optional string reason = 4;
     */
    reason?: string;

    /**
     * @generated from field: google.protobuf.Timestamp created_at = 5;
     */
    createdAt?: Timestamp;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string user_id = 6;
     */
    userId: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string name = 7;
     */
    name: string;
  };

/**
 * Describes the message api.vtubers.v1.VtuberAccessRequest.
 * Use `create(VtuberAccessRequestSchema)` to create a new message.
 */
export const VtuberAccessRequestSchema: GenMessage<VtuberAccessRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 17);

/**
 * @generated from message api.vtubers.v1.DenyVtuberProfileRequest
 */
export type DenyVtuberProfileRequest =
  Message<"api.vtubers.v1.DenyVtuberProfileRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string reason = 2;
     */
    reason: string;
  };

/**
 * Describes the message api.vtubers.v1.DenyVtuberProfileRequest.
 * Use `create(DenyVtuberProfileRequestSchema)` to create a new message.
 */
export const DenyVtuberProfileRequestSchema: GenMessage<DenyVtuberProfileRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 18);

/**
 * @generated from message api.vtubers.v1.UpdateVtuberProfileResponse
 */
export type UpdateVtuberProfileResponse =
  Message<"api.vtubers.v1.UpdateVtuberProfileResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.vtubers.v1.UpdateVtuberProfileResponse.
 * Use `create(UpdateVtuberProfileResponseSchema)` to create a new message.
 */
export const UpdateVtuberProfileResponseSchema: GenMessage<UpdateVtuberProfileResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 19);

/**
 * @generated from message api.vtubers.v1.VerifyVtuberProfileResponse
 */
export type VerifyVtuberProfileResponse =
  Message<"api.vtubers.v1.VerifyVtuberProfileResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.vtubers.v1.VerifyVtuberProfileResponse.
 * Use `create(VerifyVtuberProfileResponseSchema)` to create a new message.
 */
export const VerifyVtuberProfileResponseSchema: GenMessage<VerifyVtuberProfileResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 20);

/**
 * @generated from message api.vtubers.v1.CreateVtuberProfileAccessResponse
 */
export type CreateVtuberProfileAccessResponse =
  Message<"api.vtubers.v1.CreateVtuberProfileAccessResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.vtubers.v1.CreateVtuberProfileAccessResponse.
 * Use `create(CreateVtuberProfileAccessResponseSchema)` to create a new message.
 */
export const CreateVtuberProfileAccessResponseSchema: GenMessage<CreateVtuberProfileAccessResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 21);

/**
 * @generated from message api.vtubers.v1.DenyVtuberProfileResponse
 */
export type DenyVtuberProfileResponse =
  Message<"api.vtubers.v1.DenyVtuberProfileResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.vtubers.v1.DenyVtuberProfileResponse.
 * Use `create(DenyVtuberProfileResponseSchema)` to create a new message.
 */
export const DenyVtuberProfileResponseSchema: GenMessage<DenyVtuberProfileResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberprofiles, 22);

/**
 * @generated from service api.vtubers.v1.VtuberProfilesService
 */
export const VtuberProfilesService: GenService<{
  /**
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.AddVtuberProfile
   */
  addVtuberProfile: {
    methodKind: "unary";
    input: typeof AddVtuberProfileRequestSchema;
    output: typeof AddVtuberProfileResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.GetVtuberProfileById
   */
  getVtuberProfileById: {
    methodKind: "unary";
    input: typeof GetVtuberProfileByIdRequestSchema;
    output: typeof GetVtuberProfileByIdResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.GetVtuberProfile
   */
  getVtuberProfile: {
    methodKind: "unary";
    input: typeof GetVtuberProfileRequestSchema;
    output: typeof GetVtuberProfileByIdResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.UpdateVtuberProfile
   */
  updateVtuberProfile: {
    methodKind: "unary";
    input: typeof UpdateVtuberProfileRequestSchema;
    output: typeof UpdateVtuberProfileResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.GetAllVtuberProfiles
   */
  getAllVtuberProfiles: {
    methodKind: "unary";
    input: typeof GetAllVtuberProfilesRequestSchema;
    output: typeof GetAllVtuberProfilesResponseSchema;
  };
  /**
   * rpc DeleteVtuberProfileById(DeleteVtuberProfileByIdRequest) returns (api.shared.v1.GenericResponse) {
   *   option (api.authz.v1.options) = {
   *     require: true
   *   };
   * }
   *
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.VerifyVtuberProfile
   */
  verifyVtuberProfile: {
    methodKind: "unary";
    input: typeof VerifyVtuberProfileRequestSchema;
    output: typeof VerifyVtuberProfileResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.ApplyVtuberAccess
   */
  applyVtuberAccess: {
    methodKind: "unary";
    input: typeof CreateVtuberProfileAccessRequestSchema;
    output: typeof CreateVtuberProfileAccessResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.GetMyVtuberAccessRequests
   */
  getMyVtuberAccessRequests: {
    methodKind: "unary";
    input: typeof GetAllVtuberProfileAccessRequestSchema;
    output: typeof VtuberAccessRequestSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.GetAllVtuberProfileAccess
   */
  getAllVtuberProfileAccess: {
    methodKind: "unary";
    input: typeof GetAllVtuberProfileAccessRequestSchema;
    output: typeof GetAllVtuberProfileAccessResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.DenyVtuberProfile
   */
  denyVtuberProfile: {
    methodKind: "unary";
    input: typeof DenyVtuberProfileRequestSchema;
    output: typeof DenyVtuberProfileResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.UpdateVtuberAccessRequest
   */
  updateVtuberAccessRequest: {
    methodKind: "unary";
    input: typeof UpdateVtuberProfileAccessRequestSchema;
    output: typeof UpdateVtuberProfileAccessResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberProfilesService.SearchVtuberProfiles
   */
  searchVtuberProfiles: {
    methodKind: "unary";
    input: typeof SearchVtuberProfilesRequestSchema;
    output: typeof GetAllVtuberProfilesResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_vtubers_v1_vtuberprofiles, 0);
