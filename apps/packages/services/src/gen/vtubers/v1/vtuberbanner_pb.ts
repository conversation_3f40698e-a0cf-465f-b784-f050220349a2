// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file vtubers/v1/vtuberbanner.proto (package api.vtubers.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file vtubers/v1/vtuberbanner.proto.
 */
export const file_vtubers_v1_vtuberbanner: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Ch12dHViZXJzL3YxL3Z0dWJlcmJhbm5lci5wcm90bxIOYXBpLnZ0dWJlcnMudjEiJwoWQWRkVnR1YmVyQmFubmVyUmVxdWVzdBINCgVpbWFnZRgBIAEoCSKcAQoMVnR1YmVyQmFubmVyEgoKAmlkGAEgASgJEhEKCXZ0dWJlcl9pZBgCIAEoCRINCgVpbWFnZRgDIAEoCRIuCgpjcmVhdGVkX2F0GAQgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcBIuCgp1cGRhdGVkX2F0GAUgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcCJFChdBZGRWdHViZXJCYW5uZXJSZXNwb25zZRIqCgRkYXRhGAEgASgLMhwuYXBpLnZ0dWJlcnMudjEuVnR1YmVyQmFubmVyIisKHURlbGV0ZVZ0dWJlckJhbm5lckJ5SWRSZXF1ZXN0EgoKAmlkGAEgASgJIjoKHVVwZGF0ZVZ0dWJlckJhbm5lckJ5SWRSZXF1ZXN0EgoKAmlkGAEgASgJEg0KBWltYWdlGAIgASgJIjUKIEdldFZ0dWJlckJhbm5lckJ5VnR1YmVySWRSZXF1ZXN0EhEKCXZ0dWJlcl9pZBgBIAEoCSJPCiFHZXRWdHViZXJCYW5uZXJCeVZ0dWJlcklkUmVzcG9uc2USKgoEZGF0YRgBIAMoCzIcLmFwaS52dHViZXJzLnYxLlZ0dWJlckJhbm5lciJCCh5EZWxldGVWdHViZXJCYW5uZXJCeUlkUmVzcG9uc2USDwoHc3VjY2VzcxgBIAEoCBIPCgdtZXNzYWdlGAIgASgJIkIKHlVwZGF0ZVZ0dWJlckJhbm5lckJ5SWRSZXNwb25zZRIPCgdzdWNjZXNzGAEgASgIEg8KB21lc3NhZ2UYAiABKAkyjgQKE1Z0dWJlckJhbm5lclNlcnZpY2USbAoPQWRkVnR1YmVyQmFubmVyEiYuYXBpLnZ0dWJlcnMudjEuQWRkVnR1YmVyQmFubmVyUmVxdWVzdBonLmFwaS52dHViZXJzLnYxLkFkZFZ0dWJlckJhbm5lclJlc3BvbnNlIgiCtRgECAEYARKAAQoZR2V0VnR1YmVyQmFubmVyQnlWdHViZXJJZBIwLmFwaS52dHViZXJzLnYxLkdldFZ0dWJlckJhbm5lckJ5VnR1YmVySWRSZXF1ZXN0GjEuYXBpLnZ0dWJlcnMudjEuR2V0VnR1YmVyQmFubmVyQnlWdHViZXJJZFJlc3BvbnNlEoEBChZEZWxldGVWdHViZXJCYW5uZXJCeUlkEi0uYXBpLnZ0dWJlcnMudjEuRGVsZXRlVnR1YmVyQmFubmVyQnlJZFJlcXVlc3QaLi5hcGkudnR1YmVycy52MS5EZWxldGVWdHViZXJCYW5uZXJCeUlkUmVzcG9uc2UiCIK1GAQIARgBEoEBChZVcGRhdGVWdHViZXJCYW5uZXJCeUlkEi0uYXBpLnZ0dWJlcnMudjEuVXBkYXRlVnR1YmVyQmFubmVyQnlJZFJlcXVlc3QaLi5hcGkudnR1YmVycy52MS5VcGRhdGVWdHViZXJCYW5uZXJCeUlkUmVzcG9uc2UiCIK1GAQIARgBQjRaMmdpdGh1Yi5jb20vbnNwLWluYy92dHViZXIvYXBpL3Z0dWJlcnMvdjE7dnR1YmVyc3YxYgZwcm90bzM",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.vtubers.v1.AddVtuberBannerRequest
 */
export type AddVtuberBannerRequest =
  Message<"api.vtubers.v1.AddVtuberBannerRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string image = 1;
     */
    image: string;
  };

/**
 * Describes the message api.vtubers.v1.AddVtuberBannerRequest.
 * Use `create(AddVtuberBannerRequestSchema)` to create a new message.
 */
export const AddVtuberBannerRequestSchema: GenMessage<AddVtuberBannerRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberbanner, 0);

/**
 * @generated from message api.vtubers.v1.VtuberBanner
 */
export type VtuberBanner = Message<"api.vtubers.v1.VtuberBanner"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string vtuber_id = 2;
   */
  vtuberId: string;

  /**
   * @generated from field: string image = 3;
   */
  image: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 4;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 5;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message api.vtubers.v1.VtuberBanner.
 * Use `create(VtuberBannerSchema)` to create a new message.
 */
export const VtuberBannerSchema: GenMessage<VtuberBanner> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberbanner, 1);

/**
 * @generated from message api.vtubers.v1.AddVtuberBannerResponse
 */
export type AddVtuberBannerResponse =
  Message<"api.vtubers.v1.AddVtuberBannerResponse"> & {
    /**
     * @generated from field: api.vtubers.v1.VtuberBanner data = 1;
     */
    data?: VtuberBanner;
  };

/**
 * Describes the message api.vtubers.v1.AddVtuberBannerResponse.
 * Use `create(AddVtuberBannerResponseSchema)` to create a new message.
 */
export const AddVtuberBannerResponseSchema: GenMessage<AddVtuberBannerResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberbanner, 2);

/**
 * @generated from message api.vtubers.v1.DeleteVtuberBannerByIdRequest
 */
export type DeleteVtuberBannerByIdRequest =
  Message<"api.vtubers.v1.DeleteVtuberBannerByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;
  };

/**
 * Describes the message api.vtubers.v1.DeleteVtuberBannerByIdRequest.
 * Use `create(DeleteVtuberBannerByIdRequestSchema)` to create a new message.
 */
export const DeleteVtuberBannerByIdRequestSchema: GenMessage<DeleteVtuberBannerByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberbanner, 3);

/**
 * @generated from message api.vtubers.v1.UpdateVtuberBannerByIdRequest
 */
export type UpdateVtuberBannerByIdRequest =
  Message<"api.vtubers.v1.UpdateVtuberBannerByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string image = 2;
     */
    image: string;
  };

/**
 * Describes the message api.vtubers.v1.UpdateVtuberBannerByIdRequest.
 * Use `create(UpdateVtuberBannerByIdRequestSchema)` to create a new message.
 */
export const UpdateVtuberBannerByIdRequestSchema: GenMessage<UpdateVtuberBannerByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberbanner, 4);

/**
 * @generated from message api.vtubers.v1.GetVtuberBannerByVtuberIdRequest
 */
export type GetVtuberBannerByVtuberIdRequest =
  Message<"api.vtubers.v1.GetVtuberBannerByVtuberIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string vtuber_id = 1;
     */
    vtuberId: string;
  };

/**
 * Describes the message api.vtubers.v1.GetVtuberBannerByVtuberIdRequest.
 * Use `create(GetVtuberBannerByVtuberIdRequestSchema)` to create a new message.
 */
export const GetVtuberBannerByVtuberIdRequestSchema: GenMessage<GetVtuberBannerByVtuberIdRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberbanner, 5);

/**
 * @generated from message api.vtubers.v1.GetVtuberBannerByVtuberIdResponse
 */
export type GetVtuberBannerByVtuberIdResponse =
  Message<"api.vtubers.v1.GetVtuberBannerByVtuberIdResponse"> & {
    /**
     * @generated from field: repeated api.vtubers.v1.VtuberBanner data = 1;
     */
    data: VtuberBanner[];
  };

/**
 * Describes the message api.vtubers.v1.GetVtuberBannerByVtuberIdResponse.
 * Use `create(GetVtuberBannerByVtuberIdResponseSchema)` to create a new message.
 */
export const GetVtuberBannerByVtuberIdResponseSchema: GenMessage<GetVtuberBannerByVtuberIdResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberbanner, 6);

/**
 * @generated from message api.vtubers.v1.DeleteVtuberBannerByIdResponse
 */
export type DeleteVtuberBannerByIdResponse =
  Message<"api.vtubers.v1.DeleteVtuberBannerByIdResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.vtubers.v1.DeleteVtuberBannerByIdResponse.
 * Use `create(DeleteVtuberBannerByIdResponseSchema)` to create a new message.
 */
export const DeleteVtuberBannerByIdResponseSchema: GenMessage<DeleteVtuberBannerByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberbanner, 7);

/**
 * @generated from message api.vtubers.v1.UpdateVtuberBannerByIdResponse
 */
export type UpdateVtuberBannerByIdResponse =
  Message<"api.vtubers.v1.UpdateVtuberBannerByIdResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.vtubers.v1.UpdateVtuberBannerByIdResponse.
 * Use `create(UpdateVtuberBannerByIdResponseSchema)` to create a new message.
 */
export const UpdateVtuberBannerByIdResponseSchema: GenMessage<UpdateVtuberBannerByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberbanner, 8);

/**
 * @generated from service api.vtubers.v1.VtuberBannerService
 */
export const VtuberBannerService: GenService<{
  /**
   * @generated from rpc api.vtubers.v1.VtuberBannerService.AddVtuberBanner
   */
  addVtuberBanner: {
    methodKind: "unary";
    input: typeof AddVtuberBannerRequestSchema;
    output: typeof AddVtuberBannerResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberBannerService.GetVtuberBannerByVtuberId
   */
  getVtuberBannerByVtuberId: {
    methodKind: "unary";
    input: typeof GetVtuberBannerByVtuberIdRequestSchema;
    output: typeof GetVtuberBannerByVtuberIdResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberBannerService.DeleteVtuberBannerById
   */
  deleteVtuberBannerById: {
    methodKind: "unary";
    input: typeof DeleteVtuberBannerByIdRequestSchema;
    output: typeof DeleteVtuberBannerByIdResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberBannerService.UpdateVtuberBannerById
   */
  updateVtuberBannerById: {
    methodKind: "unary";
    input: typeof UpdateVtuberBannerByIdRequestSchema;
    output: typeof UpdateVtuberBannerByIdResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_vtubers_v1_vtuberbanner, 0);
