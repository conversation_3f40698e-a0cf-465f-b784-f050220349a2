// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file content/v1/postlikes.proto (package api.content.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type {
  PaginationDetails,
  PaginationRequest,
} from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file content/v1/postlikes.proto.
 */
export const file_content_v1_postlikes: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Chpjb250ZW50L3YxL3Bvc3RsaWtlcy5wcm90bxIOYXBpLmNvbnRlbnQudjEiJQoSQWRkUG9zdExpa2VSZXF1ZXN0Eg8KB3Bvc3RfaWQYASABKAkiKgoXR2V0UG9zdExpa2VDb3VudFJlcXVlc3QSDwoHcG9zdF9pZBgBIAEoCSJnChtHZXRBbGxQb3N0TGlrZUJ5VXNlclJlcXVlc3QSOQoKcGFnaW5hdGlvbhgBIAEoCzIgLmFwaS5zaGFyZWQudjEuUGFnaW5hdGlvblJlcXVlc3RIAIgBAUINCgtfcGFnaW5hdGlvbiKEAQocR2V0QWxsUG9zdExpa2VCeVVzZXJSZXNwb25zZRImCgRkYXRhGAEgAygLMhguYXBpLmNvbnRlbnQudjEuUG9zdExpa2USPAoScGFnaW5hdGlvbl9kZXRhaWxzGAIgASgLMiAuYXBpLnNoYXJlZC52MS5QYWdpbmF0aW9uRGV0YWlscyJcCghQb3N0TGlrZRIPCgd1c2VyX2lkGAEgASgJEg8KB3Bvc3RfaWQYAiABKAkSLgoKY3JlYXRlZF9hdBgDIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXAiKQoYR2V0UG9zdExpa2VDb3VudFJlc3BvbnNlEg0KBWNvdW50GAEgASgDIiwKGURlbGV0ZVBvc3RMaWtlQnlJZFJlcXVlc3QSDwoHcG9zdF9pZBgBIAEoCSI+ChpEZWxldGVQb3N0TGlrZUJ5SWRSZXNwb25zZRIPCgdzdWNjZXNzGAEgASgIEg8KB21lc3NhZ2UYAiABKAkiNwoTQWRkUG9zdExpa2VSZXNwb25zZRIPCgdzdWNjZXNzGAEgASgIEg8KB21lc3NhZ2UYAiABKAkyzgMKD1Bvc3RMaWtlU2VydmljZRJeCgtBZGRQb3N0TGlrZRIiLmFwaS5jb250ZW50LnYxLkFkZFBvc3RMaWtlUmVxdWVzdBojLmFwaS5jb250ZW50LnYxLkFkZFBvc3RMaWtlUmVzcG9uc2UiBoK1GAIIARJtChBHZXRQb3N0TGlrZUNvdW50EicuYXBpLmNvbnRlbnQudjEuR2V0UG9zdExpa2VDb3VudFJlcXVlc3QaKC5hcGkuY29udGVudC52MS5HZXRQb3N0TGlrZUNvdW50UmVzcG9uc2UiBoK1GAIIARJ3ChJHZXRQb3N0TGlrZXNPZlVzZXISKy5hcGkuY29udGVudC52MS5HZXRBbGxQb3N0TGlrZUJ5VXNlclJlcXVlc3QaLC5hcGkuY29udGVudC52MS5HZXRBbGxQb3N0TGlrZUJ5VXNlclJlc3BvbnNlIgaCtRgCCAEScwoSRGVsZXRlUG9zdExpa2VCeUlkEikuYXBpLmNvbnRlbnQudjEuRGVsZXRlUG9zdExpa2VCeUlkUmVxdWVzdBoqLmFwaS5jb250ZW50LnYxLkRlbGV0ZVBvc3RMaWtlQnlJZFJlc3BvbnNlIgaCtRgCCAFCNFoyZ2l0aHViLmNvbS9uc3AtaW5jL3Z0dWJlci9hcGkvY29udGVudC92MTtjb250ZW50djFiBnByb3RvMw",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.content.v1.AddPostLikeRequest
 */
export type AddPostLikeRequest =
  Message<"api.content.v1.AddPostLikeRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string post_id = 1;
     */
    postId: string;
  };

/**
 * Describes the message api.content.v1.AddPostLikeRequest.
 * Use `create(AddPostLikeRequestSchema)` to create a new message.
 */
export const AddPostLikeRequestSchema: GenMessage<AddPostLikeRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postlikes, 0);

/**
 * @generated from message api.content.v1.GetPostLikeCountRequest
 */
export type GetPostLikeCountRequest =
  Message<"api.content.v1.GetPostLikeCountRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string post_id = 1;
     */
    postId: string;
  };

/**
 * Describes the message api.content.v1.GetPostLikeCountRequest.
 * Use `create(GetPostLikeCountRequestSchema)` to create a new message.
 */
export const GetPostLikeCountRequestSchema: GenMessage<GetPostLikeCountRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postlikes, 1);

/**
 * @generated from message api.content.v1.GetAllPostLikeByUserRequest
 */
export type GetAllPostLikeByUserRequest =
  Message<"api.content.v1.GetAllPostLikeByUserRequest"> & {
    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.content.v1.GetAllPostLikeByUserRequest.
 * Use `create(GetAllPostLikeByUserRequestSchema)` to create a new message.
 */
export const GetAllPostLikeByUserRequestSchema: GenMessage<GetAllPostLikeByUserRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postlikes, 2);

/**
 * @generated from message api.content.v1.GetAllPostLikeByUserResponse
 */
export type GetAllPostLikeByUserResponse =
  Message<"api.content.v1.GetAllPostLikeByUserResponse"> & {
    /**
     * @generated from field: repeated api.content.v1.PostLike data = 1;
     */
    data: PostLike[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.content.v1.GetAllPostLikeByUserResponse.
 * Use `create(GetAllPostLikeByUserResponseSchema)` to create a new message.
 */
export const GetAllPostLikeByUserResponseSchema: GenMessage<GetAllPostLikeByUserResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postlikes, 3);

/**
 * @generated from message api.content.v1.PostLike
 */
export type PostLike = Message<"api.content.v1.PostLike"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string post_id = 2;
   */
  postId: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 3;
   */
  createdAt?: Timestamp;
};

/**
 * Describes the message api.content.v1.PostLike.
 * Use `create(PostLikeSchema)` to create a new message.
 */
export const PostLikeSchema: GenMessage<PostLike> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postlikes, 4);

/**
 * @generated from message api.content.v1.GetPostLikeCountResponse
 */
export type GetPostLikeCountResponse =
  Message<"api.content.v1.GetPostLikeCountResponse"> & {
    /**
     * @generated from field: int64 count = 1;
     */
    count: bigint;
  };

/**
 * Describes the message api.content.v1.GetPostLikeCountResponse.
 * Use `create(GetPostLikeCountResponseSchema)` to create a new message.
 */
export const GetPostLikeCountResponseSchema: GenMessage<GetPostLikeCountResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postlikes, 5);

/**
 * @generated from message api.content.v1.DeletePostLikeByIdRequest
 */
export type DeletePostLikeByIdRequest =
  Message<"api.content.v1.DeletePostLikeByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string post_id = 1;
     */
    postId: string;
  };

/**
 * Describes the message api.content.v1.DeletePostLikeByIdRequest.
 * Use `create(DeletePostLikeByIdRequestSchema)` to create a new message.
 */
export const DeletePostLikeByIdRequestSchema: GenMessage<DeletePostLikeByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postlikes, 6);

/**
 * @generated from message api.content.v1.DeletePostLikeByIdResponse
 */
export type DeletePostLikeByIdResponse =
  Message<"api.content.v1.DeletePostLikeByIdResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.content.v1.DeletePostLikeByIdResponse.
 * Use `create(DeletePostLikeByIdResponseSchema)` to create a new message.
 */
export const DeletePostLikeByIdResponseSchema: GenMessage<DeletePostLikeByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postlikes, 7);

/**
 * @generated from message api.content.v1.AddPostLikeResponse
 */
export type AddPostLikeResponse =
  Message<"api.content.v1.AddPostLikeResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.content.v1.AddPostLikeResponse.
 * Use `create(AddPostLikeResponseSchema)` to create a new message.
 */
export const AddPostLikeResponseSchema: GenMessage<AddPostLikeResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postlikes, 8);

/**
 * @generated from service api.content.v1.PostLikeService
 */
export const PostLikeService: GenService<{
  /**
   * @generated from rpc api.content.v1.PostLikeService.AddPostLike
   */
  addPostLike: {
    methodKind: "unary";
    input: typeof AddPostLikeRequestSchema;
    output: typeof AddPostLikeResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostLikeService.GetPostLikeCount
   */
  getPostLikeCount: {
    methodKind: "unary";
    input: typeof GetPostLikeCountRequestSchema;
    output: typeof GetPostLikeCountResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostLikeService.GetPostLikesOfUser
   */
  getPostLikesOfUser: {
    methodKind: "unary";
    input: typeof GetAllPostLikeByUserRequestSchema;
    output: typeof GetAllPostLikeByUserResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostLikeService.DeletePostLikeById
   */
  deletePostLikeById: {
    methodKind: "unary";
    input: typeof DeletePostLikeByIdRequestSchema;
    output: typeof DeletePostLikeByIdResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_content_v1_postlikes, 0);
