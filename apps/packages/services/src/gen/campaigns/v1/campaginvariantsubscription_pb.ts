// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file campaigns/v1/campaginvariantsubscription.proto (package api.camapigns.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";

/**
 * Describes the file campaigns/v1/campaginvariantsubscription.proto.
 */
export const file_campaigns_v1_campaginvariantsubscription: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Ci5jYW1wYWlnbnMvdjEvY2FtcGFnaW52YXJpYW50c3Vic2NyaXB0aW9uLnByb3RvEhBhcGkuY2FtYXBpZ25zLnYxIoQBCiVBZGRDYW1wYWlnblZhcmlhbnRTdWJzY3JpcHRpb25SZXF1ZXN0EhsKE2NhbXBhaWduX3ZhcmlhbnRfaWQYASABKAkSHAoUdXNlcl9iaWxsaW5nX2luZm9faWQYAiABKAkSFAoHY29tbWVudBgDIAEoCUgAiAEBQgoKCF9jb21tZW50ImUKJkFkZENhbXBhaWduVmFyaWFudFN1YnNjcmlwdGlvblJlc3BvbnNlEjsKBGRhdGEYASABKAsyLS5hcGkuY2FtYXBpZ25zLnYxLkNhbXBhaWduVmFyaWFudFN1YnNjcmlwdGlvbiL5AQobQ2FtcGFpZ25WYXJpYW50U3Vic2NyaXB0aW9uEg8KB3VzZXJfaWQYASABKAkSGwoTY2FtcGFpZ25fdmFyaWFudF9pZBgCIAEoCRIsCgdwcm9maWxlGAMgASgLMhYuYXBpLnNoYXJlZC52MS5Qcm9maWxlSACIAQESDQoFcHJpY2UYBCABKAUSLgoKY3JlYXRlZF9hdBgFIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXASEQoJdnR1YmVyX2lkGAYgASgJEhQKB2NvbW1lbnQYByABKAlIAYgBAUIKCghfcHJvZmlsZUIKCghfY29tbWVudCJIChpTZW5kRW1haWxUb1B1cmNoYXNlUmVxdWVzdBINCgVlbWFpbBgBIAEoCRIbChNjYW1wYWlnbl92YXJpYW50X2lkGAIgASgJMsIBCiJDYW1wYWlnblZhcmlhbnRTdWJzY3JpcHRpb25TZXJ2aWNlEpsBCh5BZGRDYW1wYWlnblZhcmlhbnRTdWJzY3JpcHRpb24SNy5hcGkuY2FtYXBpZ25zLnYxLkFkZENhbXBhaWduVmFyaWFudFN1YnNjcmlwdGlvblJlcXVlc3QaOC5hcGkuY2FtYXBpZ25zLnYxLkFkZENhbXBhaWduVmFyaWFudFN1YnNjcmlwdGlvblJlc3BvbnNlIgaCtRgCCAFCOFo2Z2l0aHViLmNvbS9uc3AtaW5jL3Z0dWJlci9hcGkvY2FtcGFpZ25zL3YxO2NhbXBhaWduc3YxYgZwcm90bzM",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_profile,
    ],
  );

/**
 * @generated from message api.camapigns.v1.AddCampaignVariantSubscriptionRequest
 */
export type AddCampaignVariantSubscriptionRequest =
  Message<"api.camapigns.v1.AddCampaignVariantSubscriptionRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string campaign_variant_id = 1;
     */
    campaignVariantId: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string user_billing_info_id = 2;
     */
    userBillingInfoId: string;

    /**
     * @generated from field: optional string comment = 3;
     */
    comment?: string;
  };

/**
 * Describes the message api.camapigns.v1.AddCampaignVariantSubscriptionRequest.
 * Use `create(AddCampaignVariantSubscriptionRequestSchema)` to create a new message.
 */
export const AddCampaignVariantSubscriptionRequestSchema: GenMessage<AddCampaignVariantSubscriptionRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaginvariantsubscription, 0);

/**
 * @generated from message api.camapigns.v1.AddCampaignVariantSubscriptionResponse
 */
export type AddCampaignVariantSubscriptionResponse =
  Message<"api.camapigns.v1.AddCampaignVariantSubscriptionResponse"> & {
    /**
     * @generated from field: api.camapigns.v1.CampaignVariantSubscription data = 1;
     */
    data?: CampaignVariantSubscription;
  };

/**
 * Describes the message api.camapigns.v1.AddCampaignVariantSubscriptionResponse.
 * Use `create(AddCampaignVariantSubscriptionResponseSchema)` to create a new message.
 */
export const AddCampaignVariantSubscriptionResponseSchema: GenMessage<AddCampaignVariantSubscriptionResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaginvariantsubscription, 1);

/**
 * @generated from message api.camapigns.v1.CampaignVariantSubscription
 */
export type CampaignVariantSubscription =
  Message<"api.camapigns.v1.CampaignVariantSubscription"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string campaign_variant_id = 2;
     */
    campaignVariantId: string;

    /**
     * @generated from field: optional api.shared.v1.Profile profile = 3;
     */
    profile?: Profile;

    /**
     * @generated from field: int32 price = 4;
     */
    price: number;

    /**
     * @generated from field: google.protobuf.Timestamp created_at = 5;
     */
    createdAt?: Timestamp;

    /**
     * @generated from field: string vtuber_id = 6;
     */
    vtuberId: string;

    /**
     * @generated from field: optional string comment = 7;
     */
    comment?: string;
  };

/**
 * Describes the message api.camapigns.v1.CampaignVariantSubscription.
 * Use `create(CampaignVariantSubscriptionSchema)` to create a new message.
 */
export const CampaignVariantSubscriptionSchema: GenMessage<CampaignVariantSubscription> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaginvariantsubscription, 2);

/**
 * @generated from message api.camapigns.v1.SendEmailToPurchaseRequest
 */
export type SendEmailToPurchaseRequest =
  Message<"api.camapigns.v1.SendEmailToPurchaseRequest"> & {
    /**
     * @gotag: validate:"required,email"
     *
     * @generated from field: string email = 1;
     */
    email: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string campaign_variant_id = 2;
     */
    campaignVariantId: string;
  };

/**
 * Describes the message api.camapigns.v1.SendEmailToPurchaseRequest.
 * Use `create(SendEmailToPurchaseRequestSchema)` to create a new message.
 */
export const SendEmailToPurchaseRequestSchema: GenMessage<SendEmailToPurchaseRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaginvariantsubscription, 3);

/**
 * @generated from service api.camapigns.v1.CampaignVariantSubscriptionService
 */
export const CampaignVariantSubscriptionService: GenService<{
  /**
   * @generated from rpc api.camapigns.v1.CampaignVariantSubscriptionService.AddCampaignVariantSubscription
   */
  addCampaignVariantSubscription: {
    methodKind: "unary";
    input: typeof AddCampaignVariantSubscriptionRequestSchema;
    output: typeof AddCampaignVariantSubscriptionResponseSchema;
  };
}> =
  /*@__PURE__*/
  serviceDesc(file_campaigns_v1_campaginvariantsubscription, 0);
