// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file events/v1/eventcomment.proto (package api.events.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type {
  PaginationDetails,
  PaginationRequest,
} from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import type { Profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file events/v1/eventcomment.proto.
 */
export const file_events_v1_eventcomment: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.events.v1.AddEventCommentRequest
 */
export type AddEventCommentRequest =
  Message<"api.events.v1.AddEventCommentRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string content = 1;
     */
    content: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string event_id = 2;
     */
    eventId: string;

    /**
     * @generated from field: optional bool as_vtuber = 3;
     */
    asVtuber?: boolean;

    /**
     * @generated from field: optional string parent_id = 4;
     */
    parentId?: string;
  };

/**
 * Describes the message api.events.v1.AddEventCommentRequest.
 * Use `create(AddEventCommentRequestSchema)` to create a new message.
 */
export const AddEventCommentRequestSchema: GenMessage<AddEventCommentRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventcomment, 0);

/**
 * @generated from message api.events.v1.AddEventCommentResponse
 */
export type AddEventCommentResponse =
  Message<"api.events.v1.AddEventCommentResponse"> & {
    /**
     * @generated from field: api.events.v1.EventComment data = 1;
     */
    data?: EventComment;
  };

/**
 * Describes the message api.events.v1.AddEventCommentResponse.
 * Use `create(AddEventCommentResponseSchema)` to create a new message.
 */
export const AddEventCommentResponseSchema: GenMessage<AddEventCommentResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventcomment, 1);

/**
 * @generated from message api.events.v1.GetAllEventCommentsRequest
 */
export type GetAllEventCommentsRequest =
  Message<"api.events.v1.GetAllEventCommentsRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string event_id = 1;
     */
    eventId: string;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.events.v1.GetAllEventCommentsRequest.
 * Use `create(GetAllEventCommentsRequestSchema)` to create a new message.
 */
export const GetAllEventCommentsRequestSchema: GenMessage<GetAllEventCommentsRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventcomment, 2);

/**
 * @generated from message api.events.v1.GetAllEventCommentsResponse
 */
export type GetAllEventCommentsResponse =
  Message<"api.events.v1.GetAllEventCommentsResponse"> & {
    /**
     * @generated from field: repeated api.events.v1.EventComment data = 1;
     */
    data: EventComment[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.events.v1.GetAllEventCommentsResponse.
 * Use `create(GetAllEventCommentsResponseSchema)` to create a new message.
 */
export const GetAllEventCommentsResponseSchema: GenMessage<GetAllEventCommentsResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventcomment, 3);

/**
 * @generated from message api.events.v1.EventComment
 */
export type EventComment = Message<"api.events.v1.EventComment"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string content = 2;
   */
  content: string;

  /**
   * @generated from field: string event_id = 3;
   */
  eventId: string;

  /**
   * @generated from field: optional string parent_id = 5;
   */
  parentId?: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 6;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: api.shared.v1.Profile user = 7;
   */
  user?: Profile;

  /**
   * @generated from field: optional api.shared.v1.Profile vtuber = 8;
   */
  vtuber?: Profile;

  /**
   * @generated from field: bool has_reply = 9;
   */
  hasReply: boolean;
};

/**
 * Describes the message api.events.v1.EventComment.
 * Use `create(EventCommentSchema)` to create a new message.
 */
export const EventCommentSchema: GenMessage<EventComment> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventcomment, 4);

/**
 * @generated from message api.events.v1.GetEventCommentByIdRequest
 */
export type GetEventCommentByIdRequest =
  Message<"api.events.v1.GetEventCommentByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.events.v1.GetEventCommentByIdRequest.
 * Use `create(GetEventCommentByIdRequestSchema)` to create a new message.
 */
export const GetEventCommentByIdRequestSchema: GenMessage<GetEventCommentByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventcomment, 5);

/**
 * @generated from message api.events.v1.GetEventCommentByIdResponse
 */
export type GetEventCommentByIdResponse =
  Message<"api.events.v1.GetEventCommentByIdResponse"> & {
    /**
     * @generated from field: api.events.v1.EventComment data = 1;
     */
    data?: EventComment;
  };

/**
 * Describes the message api.events.v1.GetEventCommentByIdResponse.
 * Use `create(GetEventCommentByIdResponseSchema)` to create a new message.
 */
export const GetEventCommentByIdResponseSchema: GenMessage<GetEventCommentByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventcomment, 6);

/**
 * @generated from message api.events.v1.DeleteEventCommentByIdRequest
 */
export type DeleteEventCommentByIdRequest =
  Message<"api.events.v1.DeleteEventCommentByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;
  };

/**
 * Describes the message api.events.v1.DeleteEventCommentByIdRequest.
 * Use `create(DeleteEventCommentByIdRequestSchema)` to create a new message.
 */
export const DeleteEventCommentByIdRequestSchema: GenMessage<DeleteEventCommentByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventcomment, 7);

/**
 * @generated from message api.events.v1.UpdateEventCommentByIdRequest
 */
export type UpdateEventCommentByIdRequest =
  Message<"api.events.v1.UpdateEventCommentByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string content = 1;
     */
    content: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 2;
     */
    id: string;
  };

/**
 * Describes the message api.events.v1.UpdateEventCommentByIdRequest.
 * Use `create(UpdateEventCommentByIdRequestSchema)` to create a new message.
 */
export const UpdateEventCommentByIdRequestSchema: GenMessage<UpdateEventCommentByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventcomment, 8);

/**
 * @generated from message api.events.v1.UpdateEventCommentByIdResponse
 */
export type UpdateEventCommentByIdResponse =
  Message<"api.events.v1.UpdateEventCommentByIdResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.events.v1.UpdateEventCommentByIdResponse.
 * Use `create(UpdateEventCommentByIdResponseSchema)` to create a new message.
 */
export const UpdateEventCommentByIdResponseSchema: GenMessage<UpdateEventCommentByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventcomment, 9);

/**
 * @generated from message api.events.v1.DeleteEventCommentByIdResponse
 */
export type DeleteEventCommentByIdResponse =
  Message<"api.events.v1.DeleteEventCommentByIdResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.events.v1.DeleteEventCommentByIdResponse.
 * Use `create(DeleteEventCommentByIdResponseSchema)` to create a new message.
 */
export const DeleteEventCommentByIdResponseSchema: GenMessage<DeleteEventCommentByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventcomment, 10);

/**
 * @generated from service api.events.v1.EventCommentService
 */
export const EventCommentService: GenService<{
  /**
   * @generated from rpc api.events.v1.EventCommentService.AddEventComment
   */
  addEventComment: {
    methodKind: "unary";
    input: typeof AddEventCommentRequestSchema;
    output: typeof AddEventCommentResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventCommentService.GetAllEventComments
   */
  getAllEventComments: {
    methodKind: "unary";
    input: typeof GetAllEventCommentsRequestSchema;
    output: typeof GetAllEventCommentsResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventCommentService.GetAllRepliesOfEventComment
   */
  getAllRepliesOfEventComment: {
    methodKind: "unary";
    input: typeof GetEventCommentByIdRequestSchema;
    output: typeof GetAllEventCommentsResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventCommentService.GetEventCommentById
   */
  getEventCommentById: {
    methodKind: "unary";
    input: typeof GetEventCommentByIdRequestSchema;
    output: typeof GetEventCommentByIdResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventCommentService.DeleteEventCommentById
   */
  deleteEventCommentById: {
    methodKind: "unary";
    input: typeof DeleteEventCommentByIdRequestSchema;
    output: typeof DeleteEventCommentByIdResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventCommentService.UpdateEventCommentById
   */
  updateEventCommentById: {
    methodKind: "unary";
    input: typeof UpdateEventCommentByIdRequestSchema;
    output: typeof UpdateEventCommentByIdResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_events_v1_eventcomment, 0);
