// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file taxonomy/v1/vtuber_categories.proto (package api.taxonomy.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { GenericResponseSchema } from "../../shared/v1/generic_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file taxonomy/v1/vtuber_categories.proto.
 */
export const file_taxonomy_v1_vtuber_categories: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiN0YXhvbm9teS92MS92dHViZXJfY2F0ZWdvcmllcy5wcm90bxIPYXBpLnRheG9ub215LnYxIlsKGEFkZFZ0dWJlckNhdGVnb3J5UmVxdWVzdBIMCgRuYW1lGAEgASgJEhMKC2Rlc2NyaXB0aW9uGAIgASgJEhIKBWltYWdlGAMgASgJSACIAQFCCAoGX2ltYWdlIkoKGUFkZFZ0dWJlckNhdGVnb3J5UmVzcG9uc2USLQoEZGF0YRgBIAEoCzIfLmFwaS50YXhvbm9teS52MS5WdHViZXJDYXRlZ29yeSImChhHZXRWdHViZXJDYXRlZ29yeVJlcXVlc3QSCgoCaWQYASABKAkiSgoZR2V0VnR1YmVyQ2F0ZWdvcnlSZXNwb25zZRItCgRkYXRhGAEgASgLMh8uYXBpLnRheG9ub215LnYxLlZ0dWJlckNhdGVnb3J5IlsKG1VwZGF0ZVZ0dWJlckNhdGVnb3J5UmVxdWVzdBIKCgJpZBgBIAEoCRIMCgRuYW1lGAIgASgJEhMKC2Rlc2NyaXB0aW9uGAMgASgJEg0KBWltYWdlGAQgASgJIikKG0RlbGV0ZVZ0dWJlckNhdGVnb3J5UmVxdWVzdBIKCgJpZBgBIAEoCSJ9Cg5WdHViZXJDYXRlZ29yeRIKCgJpZBgBIAEoCRIMCgRuYW1lGAIgASgJEhMKC2Rlc2NyaXB0aW9uGAMgASgJEi4KCmNyZWF0ZWRfYXQYBCABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wEgwKBHNsdWcYBSABKAkiHwodR2V0QWxsVnR1YmVyQ2F0ZWdvcmllc1JlcXVlc3QiVQoeR2V0QWxsVnR1YmVyQ2F0ZWdvcmllc1Jlc3BvbnNlEjMKCmNhdGVnb3JpZXMYASADKAsyHy5hcGkudGF4b25vbXkudjEuVnR1YmVyQ2F0ZWdvcnkiIAoeR2V0VXNlZFZ0dWJlckNhdGVnb3JpZXNSZXF1ZXN0IlsKF1Z0dWJlckNhdGVnb3J5V2l0aENvdW50EjEKCGNhdGVnb3J5GAEgASgLMh8uYXBpLnRheG9ub215LnYxLlZ0dWJlckNhdGVnb3J5Eg0KBWNvdW50GAIgASgFIl8KH0dldFVzZWRWdHViZXJDYXRlZ29yaWVzUmVzcG9uc2USPAoKY2F0ZWdvcmllcxgBIAMoCzIoLmFwaS50YXhvbm9teS52MS5WdHViZXJDYXRlZ29yeVdpdGhDb3VudDLFBQoVVnR1YmVyQ2F0ZWdvcnlTZXJ2aWNlEm4KC0FkZENhdGVnb3J5EikuYXBpLnRheG9ub215LnYxLkFkZFZ0dWJlckNhdGVnb3J5UmVxdWVzdBoqLmFwaS50YXhvbm9teS52MS5BZGRWdHViZXJDYXRlZ29yeVJlc3BvbnNlIgiCtRgECAEQARJ5ChZHZXRBbGxWdHViZXJDYXRlZ29yaWVzEi4uYXBpLnRheG9ub215LnYxLkdldEFsbFZ0dWJlckNhdGVnb3JpZXNSZXF1ZXN0Gi8uYXBpLnRheG9ub215LnYxLkdldEFsbFZ0dWJlckNhdGVnb3JpZXNSZXNwb25zZRJqChFHZXRWdHViZXJDYXRlZ29yeRIpLmFwaS50YXhvbm9teS52MS5HZXRWdHViZXJDYXRlZ29yeVJlcXVlc3QaKi5hcGkudGF4b25vbXkudjEuR2V0VnR1YmVyQ2F0ZWdvcnlSZXNwb25zZRJoCg5VcGRhdGVDYXRlZ29yeRIsLmFwaS50YXhvbm9teS52MS5VcGRhdGVWdHViZXJDYXRlZ29yeVJlcXVlc3QaHi5hcGkuc2hhcmVkLnYxLkdlbmVyaWNSZXNwb25zZSIIgrUYBAgBEAESaAoORGVsZXRlQ2F0ZWdvcnkSLC5hcGkudGF4b25vbXkudjEuRGVsZXRlVnR1YmVyQ2F0ZWdvcnlSZXF1ZXN0Gh4uYXBpLnNoYXJlZC52MS5HZW5lcmljUmVzcG9uc2UiCIK1GAQIARABEoABChFHZXRVc2VkQ2F0ZWdvcmllcxIvLmFwaS50YXhvbm9teS52MS5HZXRVc2VkVnR1YmVyQ2F0ZWdvcmllc1JlcXVlc3QaMC5hcGkudGF4b25vbXkudjEuR2V0VXNlZFZ0dWJlckNhdGVnb3JpZXNSZXNwb25zZSIIgrUYBAgBEAFCNlo0Z2l0aHViLmNvbS9uc3AtaW5jL3Z0dWJlci9hcGkvdGF4b25vbXkvdjE7dGF4b25vbXl2MWIGcHJvdG8z",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.taxonomy.v1.AddVtuberCategoryRequest
 */
export type AddVtuberCategoryRequest =
  Message<"api.taxonomy.v1.AddVtuberCategoryRequest"> & {
    /**
     * @gotag: validate:"required,min=1"
     *
     * @generated from field: string name = 1;
     */
    name: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @generated from field: optional string image = 3;
     */
    image?: string;
  };

/**
 * Describes the message api.taxonomy.v1.AddVtuberCategoryRequest.
 * Use `create(AddVtuberCategoryRequestSchema)` to create a new message.
 */
export const AddVtuberCategoryRequestSchema: GenMessage<AddVtuberCategoryRequest> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 0);

/**
 * @generated from message api.taxonomy.v1.AddVtuberCategoryResponse
 */
export type AddVtuberCategoryResponse =
  Message<"api.taxonomy.v1.AddVtuberCategoryResponse"> & {
    /**
     * @generated from field: api.taxonomy.v1.VtuberCategory data = 1;
     */
    data?: VtuberCategory;
  };

/**
 * Describes the message api.taxonomy.v1.AddVtuberCategoryResponse.
 * Use `create(AddVtuberCategoryResponseSchema)` to create a new message.
 */
export const AddVtuberCategoryResponseSchema: GenMessage<AddVtuberCategoryResponse> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 1);

/**
 * @generated from message api.taxonomy.v1.GetVtuberCategoryRequest
 */
export type GetVtuberCategoryRequest =
  Message<"api.taxonomy.v1.GetVtuberCategoryRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;
  };

/**
 * Describes the message api.taxonomy.v1.GetVtuberCategoryRequest.
 * Use `create(GetVtuberCategoryRequestSchema)` to create a new message.
 */
export const GetVtuberCategoryRequestSchema: GenMessage<GetVtuberCategoryRequest> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 2);

/**
 * @generated from message api.taxonomy.v1.GetVtuberCategoryResponse
 */
export type GetVtuberCategoryResponse =
  Message<"api.taxonomy.v1.GetVtuberCategoryResponse"> & {
    /**
     * @generated from field: api.taxonomy.v1.VtuberCategory data = 1;
     */
    data?: VtuberCategory;
  };

/**
 * Describes the message api.taxonomy.v1.GetVtuberCategoryResponse.
 * Use `create(GetVtuberCategoryResponseSchema)` to create a new message.
 */
export const GetVtuberCategoryResponseSchema: GenMessage<GetVtuberCategoryResponse> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 3);

/**
 * @generated from message api.taxonomy.v1.UpdateVtuberCategoryRequest
 */
export type UpdateVtuberCategoryRequest =
  Message<"api.taxonomy.v1.UpdateVtuberCategoryRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string name = 2;
     */
    name: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string description = 3;
     */
    description: string;

    /**
     * @generated from field: string image = 4;
     */
    image: string;
  };

/**
 * Describes the message api.taxonomy.v1.UpdateVtuberCategoryRequest.
 * Use `create(UpdateVtuberCategoryRequestSchema)` to create a new message.
 */
export const UpdateVtuberCategoryRequestSchema: GenMessage<UpdateVtuberCategoryRequest> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 4);

/**
 * @generated from message api.taxonomy.v1.DeleteVtuberCategoryRequest
 */
export type DeleteVtuberCategoryRequest =
  Message<"api.taxonomy.v1.DeleteVtuberCategoryRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;
  };

/**
 * Describes the message api.taxonomy.v1.DeleteVtuberCategoryRequest.
 * Use `create(DeleteVtuberCategoryRequestSchema)` to create a new message.
 */
export const DeleteVtuberCategoryRequestSchema: GenMessage<DeleteVtuberCategoryRequest> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 5);

/**
 * @generated from message api.taxonomy.v1.VtuberCategory
 */
export type VtuberCategory = Message<"api.taxonomy.v1.VtuberCategory"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 4;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: string slug = 5;
   */
  slug: string;
};

/**
 * Describes the message api.taxonomy.v1.VtuberCategory.
 * Use `create(VtuberCategorySchema)` to create a new message.
 */
export const VtuberCategorySchema: GenMessage<VtuberCategory> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 6);

/**
 * @generated from message api.taxonomy.v1.GetAllVtuberCategoriesRequest
 */
export type GetAllVtuberCategoriesRequest =
  Message<"api.taxonomy.v1.GetAllVtuberCategoriesRequest"> & {};

/**
 * Describes the message api.taxonomy.v1.GetAllVtuberCategoriesRequest.
 * Use `create(GetAllVtuberCategoriesRequestSchema)` to create a new message.
 */
export const GetAllVtuberCategoriesRequestSchema: GenMessage<GetAllVtuberCategoriesRequest> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 7);

/**
 * @generated from message api.taxonomy.v1.GetAllVtuberCategoriesResponse
 */
export type GetAllVtuberCategoriesResponse =
  Message<"api.taxonomy.v1.GetAllVtuberCategoriesResponse"> & {
    /**
     * @generated from field: repeated api.taxonomy.v1.VtuberCategory categories = 1;
     */
    categories: VtuberCategory[];
  };

/**
 * Describes the message api.taxonomy.v1.GetAllVtuberCategoriesResponse.
 * Use `create(GetAllVtuberCategoriesResponseSchema)` to create a new message.
 */
export const GetAllVtuberCategoriesResponseSchema: GenMessage<GetAllVtuberCategoriesResponse> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 8);

/**
 * @generated from message api.taxonomy.v1.GetUsedVtuberCategoriesRequest
 */
export type GetUsedVtuberCategoriesRequest =
  Message<"api.taxonomy.v1.GetUsedVtuberCategoriesRequest"> & {};

/**
 * Describes the message api.taxonomy.v1.GetUsedVtuberCategoriesRequest.
 * Use `create(GetUsedVtuberCategoriesRequestSchema)` to create a new message.
 */
export const GetUsedVtuberCategoriesRequestSchema: GenMessage<GetUsedVtuberCategoriesRequest> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 9);

/**
 * @generated from message api.taxonomy.v1.VtuberCategoryWithCount
 */
export type VtuberCategoryWithCount =
  Message<"api.taxonomy.v1.VtuberCategoryWithCount"> & {
    /**
     * @generated from field: api.taxonomy.v1.VtuberCategory category = 1;
     */
    category?: VtuberCategory;

    /**
     * Number of vtubers in this category
     *
     * @generated from field: int32 count = 2;
     */
    count: number;
  };

/**
 * Describes the message api.taxonomy.v1.VtuberCategoryWithCount.
 * Use `create(VtuberCategoryWithCountSchema)` to create a new message.
 */
export const VtuberCategoryWithCountSchema: GenMessage<VtuberCategoryWithCount> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 10);

/**
 * @generated from message api.taxonomy.v1.GetUsedVtuberCategoriesResponse
 */
export type GetUsedVtuberCategoriesResponse =
  Message<"api.taxonomy.v1.GetUsedVtuberCategoriesResponse"> & {
    /**
     * @generated from field: repeated api.taxonomy.v1.VtuberCategoryWithCount categories = 1;
     */
    categories: VtuberCategoryWithCount[];
  };

/**
 * Describes the message api.taxonomy.v1.GetUsedVtuberCategoriesResponse.
 * Use `create(GetUsedVtuberCategoriesResponseSchema)` to create a new message.
 */
export const GetUsedVtuberCategoriesResponseSchema: GenMessage<GetUsedVtuberCategoriesResponse> =
  /*@__PURE__*/
  messageDesc(file_taxonomy_v1_vtuber_categories, 11);

/**
 * @generated from service api.taxonomy.v1.VtuberCategoryService
 */
export const VtuberCategoryService: GenService<{
  /**
   * @generated from rpc api.taxonomy.v1.VtuberCategoryService.AddCategory
   */
  addCategory: {
    methodKind: "unary";
    input: typeof AddVtuberCategoryRequestSchema;
    output: typeof AddVtuberCategoryResponseSchema;
  };
  /**
   * @generated from rpc api.taxonomy.v1.VtuberCategoryService.GetAllVtuberCategories
   */
  getAllVtuberCategories: {
    methodKind: "unary";
    input: typeof GetAllVtuberCategoriesRequestSchema;
    output: typeof GetAllVtuberCategoriesResponseSchema;
  };
  /**
   * @generated from rpc api.taxonomy.v1.VtuberCategoryService.GetVtuberCategory
   */
  getVtuberCategory: {
    methodKind: "unary";
    input: typeof GetVtuberCategoryRequestSchema;
    output: typeof GetVtuberCategoryResponseSchema;
  };
  /**
   * @generated from rpc api.taxonomy.v1.VtuberCategoryService.UpdateCategory
   */
  updateCategory: {
    methodKind: "unary";
    input: typeof UpdateVtuberCategoryRequestSchema;
    output: typeof GenericResponseSchema;
  };
  /**
   * @generated from rpc api.taxonomy.v1.VtuberCategoryService.DeleteCategory
   */
  deleteCategory: {
    methodKind: "unary";
    input: typeof DeleteVtuberCategoryRequestSchema;
    output: typeof GenericResponseSchema;
  };
  /**
   * @generated from rpc api.taxonomy.v1.VtuberCategoryService.GetUsedCategories
   */
  getUsedCategories: {
    methodKind: "unary";
    input: typeof GetUsedVtuberCategoriesRequestSchema;
    output: typeof GetUsedVtuberCategoriesResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_taxonomy_v1_vtuber_categories, 0);
