import { useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { buttonVariants } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { ScrollArea } from "@vtuber/ui/components/scroll-area";
import { Separator } from "@vtuber/ui/components/separator";
import {
  StepItem,
  StepNextButton,
  Stepper,
  StepperContent,
} from "@vtuber/ui/components/stepper";
import { cn } from "@vtuber/ui/lib/utils";
import { ChevronRight } from "lucide-react";
import { useState } from "react";
import { CancellationProcedureFinalStep } from "./cancellation-procedure-final-step";
import { CancellationProcedureStepOne } from "./cancellation-procedure-step-one";
import { CancellationProcedureStepTwo } from "./cancellation-procedure-step-two";

export const CancellationProcedure = () => {
  const [state, setState] = useState<string[]>([]);
  const [step, setStep] = useState(1);
  const navigate = useNavigate();
  const { getText } = useLanguage();
  const onValueChange = (val: string) => {
    setState((prev) => {
      if (prev.includes(val)) {
        return prev.filter((item) => item !== val);
      }
      return [...prev, val];
    });
  };
  return (
    <Dialog>
      <DialogTrigger className="w-full">
        <button
          className={
            "w-full flex items-center justify-between py-6 px-2 hover:text-primary capitalize text-font"
          }>
          {getText("cancellation_procedure")}
          <ChevronRight className="size-6" />
        </button>
        <Separator className="bg-font" />
      </DialogTrigger>
      <DialogContent
        withCloseButton={false}
        className="lg:max-w-[60vw] p-0 rounded-10 px-8 space-y-10">
        <DialogHeader className="justify-between flex-row items-center border-b-[3px] py-4 text-font">
          <DialogTitle className="text-2xl font-bold">
            アカウント管理
          </DialogTitle>
          <DialogClose className="rounded-full text-xs border-white border py-[6px] px-[18px]">
            {getText("close")}
          </DialogClose>
        </DialogHeader>
        <Stepper
          currentStep={step}
          onFinalStepCompleted={() => {
            navigate({
              to: "/cancellation-success",
            });
          }}
          onStepChange={setStep}>
          <ScrollArea className="flex flex-col overflow-y-auto max-h-[65dvh]">
            <div className="space-y-10 text-font">
              <section className="space-y-5">
                <div className="flex items-center gap-x-5">
                  <h6 className="font-bold text-[22px]">
                    {getText("cancellation_procedure")}
                  </h6>
                  <span className="text-[17px]">{step} / 3</span>
                </div>
                {step < 3 ? (
                  <p
                    className={cn(
                      "text-sm",
                      step === 1 ? "text-[#CF2D2E]" : "text-[#8B8B8B]",
                    )}>
                    下記の項目をご確認の上、チェックを入れてからお進みください。
                  </p>
                ) : null}
              </section>
              <StepperContent>
                <StepItem>
                  <CancellationProcedureStepOne
                    items={state}
                    onValueChange={onValueChange}
                  />
                </StepItem>
                <StepItem>
                  <CancellationProcedureStepTwo
                    items={state}
                    onValueChange={onValueChange}
                  />
                </StepItem>
                <StepItem>
                  <CancellationProcedureFinalStep
                    items={state}
                    onValueChange={onValueChange}
                  />
                </StepItem>
              </StepperContent>
            </div>
          </ScrollArea>
          <section className="flex items-center gap-x-6 pb-[39px] pt-10">
            <StepNextButton
              disabled={state.length === 0}
              size={"xl"}
              className="rounded-full font-bold text-white disabled:bg-[#4C4B4B] disabled:text-[#7D7D7D]">
              退会手続きを進める
            </StepNextButton>
            <DialogClose
              className={cn(
                buttonVariants({
                  variant: "outline",
                  size: "xl",
                }),
                "rounded-full font-bold",
              )}>
              アカウントを継続
            </DialogClose>
          </section>
        </Stepper>
      </DialogContent>
    </Dialog>
  );
};
