import { Checkbox } from "@vtuber/ui/components/checkbox";
import { InputBadge } from "@vtuber/ui/components/input-badge";
import { Label } from "@vtuber/ui/components/label";
import { Textarea } from "@vtuber/ui/components/textarea";

const itemsList = [
  {
    id: "one",
    label: "サポートや運営体制が自分に合わなかった",
  },
  {
    id: "two",
    label: "時間や労力が負担になった",
  },
  {
    id: "three",
    label: "他のサービスやプラットフォームに乗り換えた",
  },
  {
    id: "four",
    label: "ファンや支援者との関係構築が難しかった",
  },
  {
    id: "five",
    label: "資金が集まらなかった",
  },
  {
    id: "six",
    label: "方向性や目標が変わり、プラットフォームの利用目的が合わなくなった",
  },
];

interface Props {
  items: string[];
  onValueChange: (val: string) => void;
}

export const CancellationProcedureStepTwo = ({
  items,
  onValueChange,
}: Props) => {
  return (
    <div className="space-y-10 text-font">
      <section className="space-y-4">
        <div className="flex items-center gap-x-2">
          <h6 className="text-lg font-bold">
            今後のサービス改善のために退会する理由を教えてください
          </h6>
          <InputBadge />
        </div>
        <div className="grid grid-cols-2 items-center gap-[14px] gap-y-[10px]">
          {itemsList.map((item) => (
            <div
              key={item.id}
              className="flex items-center gap-x-[19px]">
              <Checkbox
                className="size-[14px] rounded-[2px] border-white data-[state=checked]:bg-transparent data-[state=checked]:border-tertiary"
                id={item.id}
                checked={items.includes(item.id)}
                indicatorClassName="text-tertiary"
                onCheckedChange={(e) => {
                  onValueChange(item.id);
                }}
              />
              <Label htmlFor={item.id}>{item.label}</Label>
            </div>
          ))}
        </div>
      </section>
      <section className="space-y-4">
        <Label
          htmlFor="content"
          className="flex items-center gap-x-2">
          <p className="text-lg font-bold">具体的にご記入ください</p>
          <InputBadge type="optional" />
        </Label>
        <Textarea
          minHeight={164}
          className="rounded-none"
          placeholder="こちらにご記入ください。"
          id="content"
        />
      </section>
    </div>
  );
};
