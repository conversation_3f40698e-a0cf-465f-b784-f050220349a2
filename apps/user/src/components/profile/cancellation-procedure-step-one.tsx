import { Checkbox } from "@vtuber/ui/components/checkbox";
import { Label } from "@vtuber/ui/components/label";

interface Props {
  items: string[];
  onValueChange: (val: string) => void;
}

export const CancellationProcedureStepOne = ({
  items,
  onValueChange,
}: Props) => {
  return (
    <div className="space-y-10">
      <section className="flex items-start gap-x-3">
        <Checkbox
          checked={items.includes("one")}
          onCheckedChange={(e) => {
            onValueChange("one");
          }}
          id="one"
          className="rounded-[2px] size-5 data-[state=checked]:bg-transparent mt-1  border-[#7A7A7A] data-[state=checked]:border-tertiary"
          indicatorClassName="text-tertiary size-5"
        />
        <div className="space-y-2">
          <Label
            htmlFor="one"
            className="text-lg font-bold">
            会員登録情報について
          </Label>
          <p>
            個人情報規定ならびに弊社のセキュリティシステム上、退会後の会員登録内容は確認できなくなります。
            <br />
            プロフィール情報などを含むすべての関連アカウント情報、公開したプロジェクト等がすべて削除されます。
          </p>
        </div>
      </section>
      <section className="flex items-start gap-x-3">
        <Checkbox
          id="two"
          checked={items.includes("two")}
          onCheckedChange={(e) => {
            onValueChange("two");
          }}
          className="rounded-[2px] size-5 data-[state=checked]:bg-transparent mt-1 border-[#7A7A7A] data-[state=checked]:border-tertiary"
          indicatorClassName="text-tertiary size-5"
        />
        <div className="space-y-2">
          <Label
            htmlFor="two"
            className="text-lg font-bold">
            ポイントについて
          </Label>
          <p>
            保有しているポイントは全て無効になります。新しい会員登録へのポイントの移行はできません。
          </p>
        </div>
      </section>
      <section className="flex items-start gap-x-3">
        <Checkbox
          checked={items.includes("three")}
          onCheckedChange={(e) => {
            onValueChange("three");
          }}
          id="three"
          className="rounded-[2px] size-5 data-[state=checked]:bg-transparent mt-1  border-[#7A7A7A] data-[state=checked]:border-tertiary"
          indicatorClassName="text-tertiary size-5"
        />
        <div className="space-y-2">
          <Label
            htmlFor="three"
            className="text-lg font-bold">
            リターンや発送について
          </Label>
          <p>
            すでにプロジェクトが完了しているリターン品は、退会後も発送いたします。
            <br />
            退会後は、リターン品等の配送先やお支払い方法などは、変更できません。{" "}
          </p>
        </div>
      </section>
      <section className="flex items-start gap-x-3">
        <Checkbox
          id="four"
          checked={items.includes("four")}
          onCheckedChange={(e) => {
            onValueChange("four");
          }}
          className="rounded-[2px] size-5 data-[state=checked]:bg-transparent mt-1  border-[#7A7A7A] data-[state=checked]:border-tertiary"
          indicatorClassName="text-tertiary size-5"
        />
        <Label
          htmlFor="four"
          className="text-lg font-bold">
          お問い合わせにご連絡をいただいても、削除した情報を復旧することはできませんが、よろしいですか？
        </Label>
      </section>
      <section className="flex items-start gap-x-3">
        <Checkbox
          id="five"
          checked={items.includes("five")}
          onCheckedChange={(e) => {
            onValueChange("five");
          }}
          className="rounded-[2px] size-5 data-[state=checked]:bg-transparent mt-1  border-[#7A7A7A] data-[state=checked]:border-tertiary"
          indicatorClassName="text-tertiary size-5"
        />
        <Label
          htmlFor="five"
          className="text-lg font-bold">
          すべての項目を確認しましたか？
        </Label>
      </section>
    </div>
  );
};
