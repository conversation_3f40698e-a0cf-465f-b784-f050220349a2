import { Checkbox } from "@vtuber/ui/components/checkbox";
import { Label } from "@vtuber/ui/components/label";

interface Props {
  items: string[];
  onValueChange: (val: string) => void;
}

export const CancellationProcedureFinalStep = ({
  items,
  onValueChange,
}: Props) => {
  return (
    <div className="flex items-start gap-x-5">
      <Checkbox
        checked={items.includes("one")}
        onCheckedChange={(e) => {
          onValueChange("one");
        }}
        id="one"
        className="rounded-[2px] size-5 data-[state=checked]:bg-transparent mt-1  border-[#7A7A7A] data-[state=checked]:border-tertiary"
        indicatorClassName="text-tertiary size-5"
      />
      <div className="text-font space-y-2">
        <Label
          htmlFor="one"
          className="text-lg font-bold">
          本当に退会しますか？
        </Label>
        <p>
          退会手続きを実行してもよろしいですか？現在と同じアカウントは元に戻りません。
          <br />
          退会手続きが完了した時点で、現在保存されている参加プロジェクトや、投稿内容などの情報が全て削除されますのでご注意ください。
        </p>
      </div>
    </div>
  );
};
