import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { cn } from "@vtuber/ui/lib/utils";

export const ContactFooter = ({ className }: { className?: string }) => {
  const { getText } = useLanguage();
  return (
    <footer
      className={cn(
        "flex flex-col items-center gap-y-[46px] text-[#747474] sm:pt-40 pt-20 sm:pb-20 pb-10",
        className,
      )}>
      <section className="flex gap-x-6 items-center text-sm font-light flex-wrap justify-center gap-y-2">
        <Link
          className="hover:underline"
          to="/terms">
          {getText("terms_of_use")}
        </Link>
        <Link
          className="hover:underline"
          to="/transaction-act">
          {getText("transaction_act")}
        </Link>
        <Link
          className="hover:underline"
          to="/privacy">
          {getText("privacy_policy")}
        </Link>
        <Link
          className="hover:underline"
          to="/operating-company">
          {getText("operating_company")}
        </Link>
      </section>
      <small className="text-xs">
        ©{new Date().getFullYear()} Vsai. All Rights Reserved.
      </small>
    </footer>
  );
};
