import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@vtuber/ui/components/carousel";
import { Container } from "@vtuber/ui/components/container";
import { ContentHeading } from "@vtuber/ui/components/content-heading";
import { DottedWithRectangleShape } from "@vtuber/ui/components/shape/dotted-with-rectangle";
import { Skeleton } from "@vtuber/ui/components/skeleton";

export const HomeComingSoonCampaignsSkeleton = () => {
  return (
    <div className="md:pt-36 pt-24">
      <Container>
        <ContentHeading
          className="sm:whitespace-normal whitespace-pre-line"
          title={`近日開催
クラウドファンディング`}
          subTitle="Coming Soon Crowdfunding"
        />
      </Container>
      <section className="md:-space-y-9 -space-y-5 md:pl-[70px] pl-5 md:pr-[70px] relative">
        <h3
          className="font-montserrat md:text-[120px] text-[56px] font-medium sm:translate-x-0 translate-x-16 text-right text-background whitespace-nowrap"
          style={{
            textShadow: "0px 0px 2px #F0940E",
          }}>
          Coming Soon
        </h3>
        <DottedWithRectangleShape className="[&_circle]:fill-blue01 [&_rect]:fill-blue01 rotate-180 absolute bottom-0 md:left-[70px] md:translate-y-28 sm:translate-y-16 translate-y-20 md:w-[112px] md:h-[201px] w-[73px] h-[132px]" />
        <Carousel
          opts={{
            dragFree: true,
          }}>
          <CarouselContent className="md:gap-x-12 gap-x-2">
            {[0, 1, 2].map((_, i) => (
              <CarouselItem
                key={i}
                className="basis-[60%]">
                <div
                  className="md:p-8 p-4 grid md:grid-cols-2 md:gap-x-8 items-center rounded-[5px] bg-[#252339]"
                  style={{
                    filter: "drop-shadow(0px 0px 70px #0E0B23)",
                  }}>
                  <AspectRatio ratio={16 / 9}>
                    <Skeleton className="size-full" />
                  </AspectRatio>
                  <div className="space-y-6 md:pt-0 pt-8">
                    <div className="space-y-0.5">
                      <Skeleton className="h-2 w-full" />
                      <Skeleton className="h-2 w-[80%]" />
                      <Skeleton className="h-2 w-[65%]" />
                    </div>
                    <Skeleton className="rounded-[3px] md:w-[139px] md:h-[45px] h-[41px] w-[80%]" />
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </section>
    </div>
  );
};
