import { useLanguage } from "@vtuber/language/hooks";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Card, CardContent, CardHeader } from "@vtuber/ui/components/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@vtuber/ui/components/carousel";
import { Container } from "@vtuber/ui/components/container";
import { ContentHeading } from "@vtuber/ui/components/content-heading";
import { DottedWithRectangleShape } from "@vtuber/ui/components/shape/dotted-with-rectangle";
import { Skeleton } from "@vtuber/ui/components/skeleton";
import { ChevronLeft, ChevronRight } from "lucide-react";

export const HomeRecommendedCampaignSkeleton = () => {
  const { getText } = useLanguage();

  return (
    <div className="space-y-[88px] bg-background relative">
      <div className="bg-sub w-full absolute md:h-[292px] h-[205px] bottom-0 left-0 md:translate-y-[100px] sm:translate-y-5 translate-y-14" />
      <DottedWithRectangleShape className="absolute bottom-0 md:right-20 right-5 md:translate-y-60 sm:translate-y-20 translate-y-[120px] md:h-[179px] md:w-[157px] w-[78px] h-[89px]" />
      <Container>
        <ContentHeading
          subTitle="Recommended Crowdfunding"
          title="おすすめクラウドファンディング"
        />
      </Container>
      <Carousel
        opts={{
          dragFree: true,
        }}>
        <CarouselContent className="md:gap-x-[60px] sm:gap-x-5 gap-x-0">
          {[0, 1, 2].map((_, i) => {
            return (
              <CarouselItem
                key={i}
                className="md:basis-[30%] sm:basis-[55%] basis-[80%]">
                <Card className="rounded-[5px] p-0 bg-background">
                  <CardHeader className="p-0">
                    <AspectRatio ratio={16 / 9}>
                      <Skeleton className="size-full" />
                    </AspectRatio>
                  </CardHeader>
                  <CardContent className="p-6 space-y-4">
                    <div className="space-y-0.5">
                      <Skeleton className="h-2 w-full" />
                      <Skeleton className="h-2 w-[80%]" />
                      <Skeleton className="h-2 w-[65%]" />
                    </div>
                    <Skeleton className="rounded-[3px] h-[40px] w-[25%]" />
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-8 w-[20%]" />
                      <Skeleton className="h-8 w-[20%]" />
                    </div>
                    <div className="space-y-[5px]">
                      <div className="flex items-center justify-between text-font">
                        <Skeleton className="h-2 w-[20%]" />
                        <Skeleton className="h-4 w-8" />
                      </div>
                      <Skeleton className="h-[10px] rounded-full w-full" />
                    </div>
                  </CardContent>
                </Card>
              </CarouselItem>
            );
          })}
        </CarouselContent>
        <CarouselPrevious
          className="md:size-[95px] sm:size-[65px] size-[44px] border border-[#E4E4E4] bg-[#5F5D75] md:left-[29%] left-4 -translate-x-0"
          style={{
            filter: "drop-shadow(0px 0px 15px rgba(255,255,255,0.25))",
          }}>
          <ChevronLeft className="text-[#E4E4E4] size-[22px]" />
        </CarouselPrevious>
        <CarouselNext
          className="md:size-[95px] sm:size-[65px] size-[44px] border border-[#E4E4E4] bg-[#5F5D75] md:right-[31%] right-4 -translate-x-0"
          style={{
            filter: "drop-shadow(0px 0px 15px rgba(255,255,255,0.25))",
          }}>
          <ChevronRight className="text-[#E4E4E4] size-[22px]" />
        </CarouselNext>
      </Carousel>
    </div>
  );
};
