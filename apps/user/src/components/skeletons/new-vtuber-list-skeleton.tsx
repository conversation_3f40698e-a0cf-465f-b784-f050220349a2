import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { buttonVariants } from "@vtuber/ui/components/button";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselIndicators,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@vtuber/ui/components/carousel";
import { Container } from "@vtuber/ui/components/container";
import { ContentHeading } from "@vtuber/ui/components/content-heading";
import { Skeleton } from "@vtuber/ui/components/skeleton";
import { cn } from "@vtuber/ui/lib/utils";
import { ArrowRight, ChevronLeft, ChevronRight } from "lucide-react";
import { motion } from "motion/react";
import { useEffect, useState } from "react";

export const NewVtuberListSkeleton = () => {
  const { getText } = useLanguage();
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!api) return;

    setCurrent(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);

  const getArcTransform = (index: number, currentIndex: number) => {
    const position = index - currentIndex;
    const distance = Math.abs(position);
    const isActive = distance === 0;

    // Arc positioning - enhanced curve effect
    const translateY = distance * distance * 80;
    const scale = isActive ? 1 : Math.max(0.7, 1 - distance * 0.2);
    const rotateY = isActive ? 0 : position * -20;
    const rotateZ = isActive ? 0 : position * 8;
    // const opacity = distance > 1 ? 0.4 : isActive ? 1 : 0.7;

    return {
      transform: `translateY(${translateY}px) scale(${scale}) rotateY(${rotateY}deg) rotateZ(${rotateZ}deg)`,
      // opacity,
      zIndex: isActive ? 10 : Math.max(1, 10 - distance),
    };
  };

  return (
    <div className="space-y-10 md:pb-0 pb-16">
      <Container>
        <ContentHeading
          subTitle="New Vtubers"
          title="新着VTuber"
        />
      </Container>
      <Carousel
        setApi={setApi}
        className="w-full"
        opts={{
          align: "center",
          loop: false,
          dragFree: false,
          slidesToScroll: 1,
          startIndex: 1,
        }}>
        <div className="md:-space-y-32 sm:-space-y-20 -space-y-[105px]">
          <h3
            className="font-montserrat md:text-[120px] md:pr-[70px] z-30 uppercase text-[56px] font-medium sm:translate-x-0 translate-x-16 text-right text-background whitespace-nowrap"
            style={{
              textShadow: "0px 0px 2px #F0940E",
            }}>
            {getText("new_vtuber")}
          </h3>
          <CarouselContent
            className="gap-0 md:h-[500px] min-h-[350px] pt-20 md:pl-16 pl-10 ml-0"
            style={{ perspective: "1200px" }}>
            {[0, 1, 2, 4, 5, 6].map((_, index) => {
              const arcStyle = getArcTransform(index, current);

              return (
                <CarouselItem
                  key={index}
                  className="basis-[73%] sm:basis-1/3 relative pl-0">
                  <motion.div
                    className="px-1"
                    style={arcStyle}
                    animate={arcStyle}
                    transition={{
                      type: "spring",
                      stiffness: 120,
                      damping: 20,
                      duration: 0.7,
                    }}>
                    <Skeleton className="absolute md:size-[100px] size-[53px] rounded-full z-20 border border-[#87E1F4] top-0 left-0 -translate-x-[45%] -translate-y-[45%]" />
                    <div className="relative overflow-hidden rounded-[20px]">
                      <AspectRatio ratio={16 / 9}>
                        <Skeleton className="size-full" />
                      </AspectRatio>
                      <div
                        className="absolute inset-0 flex items-end px-[25px] font-bold text-lg text-[#F7F5F5] py-[18px]"
                        style={{
                          background:
                            "linear-gradient(to bottom, rgba(0, 0, 0, 0) 70%, rgba(12, 9, 5, 0.80) 100%)",
                          filter: "drop-shadow(0px 0px 70px #0E0B23)",
                          WebkitFilter: "drop-shadow(0px 0px 70px #0E0B23)",
                          msFilter: "drop-shadow(0px 0px 70px #0E0B23)",
                        }}>
                        <Skeleton className="w-[70%] h-3" />
                      </div>
                    </div>
                  </motion.div>
                </CarouselItem>
              );
            })}
          </CarouselContent>
        </div>
        <div className="flex gap-x-[28px] items-center justify-center">
          <CarouselPrevious className="!static translate-x-0 translate-y-0 size-10 border-[#E4E4E4]">
            <ChevronLeft className="text-[#E4E4E4]" />
          </CarouselPrevious>
          <CarouselIndicators
            wrapperClassName="mt-0 gap-[9px]"
            className="sm:size-2 bg-[#44444A]"
            activeClassName="bg-font"
            variant="dots"
          />
          <CarouselNext className="!static translate-x-0 translate-y-0 size-10 border-[#E4E4E4]">
            <ChevronRight className="text-[#E4E4E4]" />
          </CarouselNext>
        </div>
        <div className="flex justify-center pt-12">
          <Link
            to="/vtuber"
            className={cn(
              buttonVariants({
                variant: "outline",
              }),
              "rounded-full h-[56px] px-[22px] gap-x-[32px] font-medium",
            )}>
            {getText("see_more_vtubers")}
            <ArrowRight />
          </Link>
        </div>
      </Carousel>
    </div>
  );
};
