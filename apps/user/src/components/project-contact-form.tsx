import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { But<PERSON> } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { Form } from "@vtuber/ui/components/form";
import { SelectInput } from "@vtuber/ui/components/form-inputs/select-input";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { HeaderBreadCrumb } from "@vtuber/ui/components/header-bread-crumb";
import { InputBadge } from "@vtuber/ui/components/input-badge";
import { Spinner } from "@vtuber/ui/components/spinner";
import { ArrowRight, Triangle } from "lucide-react";
import { useForm } from "react-hook-form";
import z from "zod";

const formValues = z.object({
  name: z.string(),
  projectName: z.string(),
  inquiryType: z.string(),
  email: z.string().email(),
  emailConfirm: z.string().email(),
  inquiryDetails: z.string(),
});

type FormValues = z.infer<typeof formValues>;

interface Props {
  projectName?: string;
  onSubmit: (data: FormValues, form: any) => void;
  isPending: boolean;
}

export const ProjectContactForm = ({
  projectName,
  onSubmit,
  isPending,
}: Props) => {
  const { language, getText } = useLanguage();
  const form = useForm<FormValues>({
    defaultValues: {
      name: "",
      projectName,
      inquiryType: "",
      email: "",
      emailConfirm: "",
      inquiryDetails: "",
    },
  });

  return (
    <Container className="pt-20">
      <div className="sm:pt-0 pt-3 sm:space-y-0 space-y-8">
        <HeaderBreadCrumb className="sm:hidden block" />

        <h1
          className={
            "!text-primary sm:text-h2-pc text-[28px] leading-normal font-bold text-center sm:mt-16 capitalize"
          }>
          {getText("contact_project_owner")}
        </h1>
      </div>
      <div className="md:max-w-[824px] mx-auto w-full grid gap-y-[35px] text-font sm:pt-20 pt-10">
        <h3 className="sm:text-lg text-sm leading-[180%]">
          {language === "ja"
            ? `『${projectName}』に関するご相談やご質問がございましたらお気軽にお問い合わせ下さい。`
            : `If you have any questions or concerns about the "${projectName}", please feel free to contact us.`}
        </h3>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit((v) => {
              if (v.email !== v.emailConfirm) {
                form.setError("emailConfirm", {
                  message: getText("email_confirmation_error"),
                });
                return;
              }
              onSubmit(v, form);
            })}
            className="gap-y-[72px] grid">
            <div className="grid gap-y-[35px]">
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder={getText("project_contact_name_placeholder")}
                name="name"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">{getText("name")}</p>
                    <InputBadge />
                  </div>
                }
              />
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                name="projectName"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">
                      {getText("project_name")}
                    </p>
                    <InputBadge />
                  </div>
                }
              />
              <SelectInput
                Icon={Triangle}
                options={[
                  {
                    label: getText("about_campaign"),
                    value: getText("about_campaign"),
                  },
                  {
                    label: getText("about_rewards"),
                    value: getText("about_rewards"),
                  },
                  {
                    label: getText("about_purchasing_plan"),
                    value: getText("about_purchasing_plan"),
                  },
                  {
                    label: getText("others"),
                    value: getText("others"),
                  },
                ]}
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder={getText("inquiry_type_placeholder")}
                name="inquiryType"
                className="rounded-xs h-[50px] placeholder:text-[#505050] [&_svg]:fill-font [&_svg]:rotate-180"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">
                      {getText("inquiry_type")}
                    </p>
                    <InputBadge type="required" />
                  </div>
                }
              />
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder={getText("project_contact_email_placeholder")}
                name="email"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">
                      {getText("email_address")}
                    </p>
                    <InputBadge />
                  </div>
                }
              />
              <div className="space-y-[10px]">
                <TextInput
                  control={form.control}
                  wrapperClassName="gap-y-[10px]"
                  name="emailConfirm"
                  type="email"
                  inputMode="email"
                  className="rounded-xs h-[50px] placeholder:text-[#505050]"
                  placeholder={getText("project_contact_email_placeholder")}
                  label={
                    <div className="flex items-center gap-2">
                      <p className="font-bold text-font">
                        {getText("email_confirmation")}
                      </p>
                      <InputBadge />
                    </div>
                  }
                />
                <ul className="list-disc pl-5 text-sm space-y-[18px]">
                  <li>{getText("contact_email_confirmation_details_1")}</li>
                  <li>{getText("contact_email_confirmation_details_2")}</li>
                  <li>{getText("contact_email_confirmation_details_3")}</li>
                </ul>
              </div>

              <TextAreaInput
                minHeight={215}
                className="rounded-xs placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">
                      {getText("inquiry_details")}
                    </p>
                    <InputBadge />
                  </div>
                }
                control={form.control}
                name="inquiryDetails"
                placeholder={getText("project_contact_inqiury_placeholder")}
              />
            </div>
            <div className="bg-[#2C2820] sm:p-8 p-4 rounded-xs leading-[180%]">
              <ul className="list-disc space-y-[8px] text-[#CCCCCC] font-medium pl-5">
                <li>{getText("contact_project_owner_warning")}</li>
                {language === "ja" ? (
                  <li>
                    重複した内容がないか、事前に「
                    <Link
                      to="/faq"
                      className="text-blue01 hover:underline">
                      よくある質問
                    </Link>
                    のご確認をお願いいたします。
                  </li>
                ) : (
                  <li>
                    Please check the{" "}
                    <Link
                      to="/faq"
                      className="text-blue01 hover:underline">
                      FAQ
                    </Link>{" "}
                    beforehand to make sure there is no duplicate content.
                  </li>
                )}
              </ul>
            </div>
            <Button
              variant={"tertiary"}
              className="h-[68px] base:w-[336px] w-full mx-auto text-white block pl-[46px] pr-[30px]">
              <div className=" flex justify-between items-center w-full">
                <p className="font-bold text-lg">{getText("send")}</p>
                {isPending ? <Spinner className="!size-8" /> : <ArrowRight />}
              </div>
            </Button>
          </form>
        </Form>
      </div>
    </Container>
  );
};
