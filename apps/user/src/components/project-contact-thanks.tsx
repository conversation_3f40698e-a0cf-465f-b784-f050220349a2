import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { DictionaryKey } from "@vtuber/language/types";
import { buttonVariants } from "@vtuber/ui/components/button";
import { HeaderBreadCrumb } from "@vtuber/ui/components/header-bread-crumb";
import { cn } from "@vtuber/ui/lib/utils";
import { CallToAction } from "./layout/call-to-action";

export const ContactThanks = ({
  title,
  className,
}: {
  title: DictionaryKey;
  className?: string;
}) => {
  const { getText } = useLanguage();
  return (
    <div className="pt-20">
      <div className="p-3 pb-0 sm:hidden block">
        <HeaderBreadCrumb />
      </div>
      <div className="text-font sm:max-w-[824px] mx-auto sm:px-0 px-5 flex flex-col items-center pb-40 pt-20 gap-y-16">
        <h1
          className={cn(
            "text-center font-bold sm:text-[40px] text-2xl w-fit leading-relaxed mx-auto bg-clip-text text-transparent bg-gradient-text",
            className,
          )}>
          {getText(title)}
        </h1>
        <section className="gap-y-[56px] flex flex-col items-center">
          <div className="sm:text-lg space-y-10">
            <p>{getText("project_contact_thankyou_description_1")}</p>
            <p>{getText("project_contact_thankyou_description_2")}</p>
          </div>
          <Link
            to="/"
            className={cn(
              buttonVariants({
                variant: "outline",
              }),
              "font-bold rounded-full h-[60px] sm:w-[424px] w-full mx-auto",
            )}>
            {getText("back_to_top")}
          </Link>
        </section>
      </div>
      <CallToAction />
    </div>
  );
};
