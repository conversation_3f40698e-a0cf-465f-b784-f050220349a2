import { useNavigate } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { Post } from "@vtuber/services/content";
import { toast } from "sonner";
import { MembershipModal } from "../membership-modal";

interface Props {
  children: React.ReactNode;
  post: Post;
  isPlanActive: boolean;
  onOpen: () => void;
}

export const PostMedia = ({ children, post, isPlanActive, onOpen }: Props) => {
  const { session } = useAuth();
  const { getText } = useLanguage();
  const navigate = useNavigate();
  if (!post.media) return null;
  if (!session)
    return (
      <button
        onClick={() => {
          navigate({
            to: "/login",
            search: (prev) => ({
              ...prev,
              redirect: location.pathname,
            }),
          });
        }}>
        {children}
      </button>
    );
  if (!isPlanActive)
    return (
      <button
        onClick={() => {
          toast.info(
            getText(
              "member_only_and_vtuber_not_accepting_subscriptions_message",
            ),
          );
        }}>
        {children}
      </button>
    );
  if (post.membershipOnly && !session)
    return (
      <button
        onClick={() => {
          navigate({
            to: "/login",
            search: {
              redirect: location.pathname,
            },
          });
        }}>
        {children}
      </button>
    );

  if (post.membershipOnly && session)
    return (
      <MembershipModal vtuberId={post.vtuber?.id!}>{children}</MembershipModal>
    );

  return <button onClick={onOpen}>{children}</button>;
};
