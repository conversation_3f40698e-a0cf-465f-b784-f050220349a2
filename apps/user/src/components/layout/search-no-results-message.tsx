import { useCategories } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { DisplayTag } from "@vtuber/ui/components/display-tag";

export const SearchNoResultsMessage = ({ query }: { query: string }) => {
  const { language, getText } = useLanguage();
  const { getCategoryById } = useCategories();
  const displayQuery = getCategoryById(query)?.name || query;
  return (
    <div className="sm:space-y-[52px] space-y-8 lg:max-w-[1000px] mx-auto">
      <section className="space-y-6 text-center">
        <p className="text-[32px] font-medium text-[#D4594B]">
          0
          <span className="font-medium text-base text-font">
            {getText("items")}
          </span>
        </p>
        <h3 className="text-font text-[28px] font-bold">
          {language === "ja"
            ? `“${displayQuery}” に一致する情報は見つかりませんでした。`
            : `No matching information was found for "${displayQuery}".`}
        </h3>
        <p className="font-medium text-[#D4594B] whitespace-pre-line text-center">
          {getText("no_matching_information_for_keyword")}
        </p>
      </section>
      <section className="space-y-6">
        <DisplayTag
          type="underlined"
          text="trouble_searching"
        />
        <p>{getText("having_trouble_searching")}</p>
        <div className="flex items-center gap-2 flex-wrap">
          <button className="bg-sub text-font text-sm font-medium hover:text-tertiary rounded-xs px-4 py-[9px]">
            3Dモデル制作
          </button>
          <button className="bg-sub text-font text-sm font-medium hover:text-tertiary rounded-xs px-4 py-[9px]">
            衣装制作
          </button>
          <button className="bg-sub text-font text-sm font-medium hover:text-tertiary rounded-xs px-4 py-[9px]">
            3Dモデル制作
          </button>
          <button className="bg-sub text-font text-sm font-medium hover:text-tertiary rounded-xs px-4 py-[9px]">
            衣装制作
          </button>
        </div>
      </section>
      <section className="space-y-8">
        <DisplayTag
          type="underlined"
          text="search_tips"
        />
        <ul className="list-disc list-inside text-font space-y-3">
          <li>{getText("search_note_1")}</li>
          <li>{getText("search_note_2")}</li>
          <li>{getText("search_note_3")}</li>
          <li>{getText("search_note_4")}</li>
        </ul>
      </section>
    </div>
  );
};
