import { useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import { Input } from "@vtuber/ui/components/input";
import { Label } from "@vtuber/ui/components/label";
import { addRecentSearch, CAN_USE_DOM, cn } from "@vtuber/ui/lib/utils";
import { ArrowRight, Search } from "lucide-react";
import { useState } from "react";
interface Props {
  setShowSearch?: React.Dispatch<React.SetStateAction<boolean>>;
  className?: string;
  buttonWrapperClassName?: string;
  autoFocus?: boolean;
  searchQuery?: string;
}

export const SearchBar = ({
  setShowSearch,
  className,
  buttonWrapperClassName,
  autoFocus = false,
  searchQuery,
}: Props) => {
  const navigate = useNavigate();
  const { getText } = useLanguage();
  const [query, setQuery] = useState(searchQuery || "");

  const disableButton = !query?.replaceAll(" ", "").length;
  const recentSearches = CAN_USE_DOM
    ? (JSON.parse(localStorage.getItem("recentSearches") || "[]") as string[])
    : [];

  return (
    <div className={cn("space-y-[29px]", className)}>
      <section className="space-y-[10px]">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            addRecentSearch(query);
            navigate({
              to: "/search",
              search: {
                query,
              },
            });
            setShowSearch?.(false);
          }}
          className="space-y-[10px]">
          <Label>{getText("search_by_keyword")}</Label>
          <Input
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
            }}
            autoFocus={autoFocus}
            rightIconClassName="right-0"
            rightIcon={
              <Button
                type="submit"
                className="h-full bg-[#9B95BA] hover:bg-[#9B95BA]/50 size-[56px] rounded-l-none">
                <Search />
              </Button>
            }
            className="h-[56px] rounded-xs bg-[#26243A]"
            placeholder={getText("search_bar_placeholder")}
          />
        </form>
        <div className="flex items-center gap-[26px] text-xs font-medium text-[#DADADA] flex-wrap">
          {recentSearches.map((c, i) => (
            <button
              onClick={() => {
                navigate({
                  to: "/search",
                  search: {
                    query: c,
                  },
                });
                setQuery(c);
                setShowSearch?.(false);
              }}
              className={cn(
                "hover:text-tertiary",
                c === searchQuery ? "text-tertiary" : "",
              )}
              key={i}>
              {c}
            </button>
          ))}
        </div>
      </section>
      <section
        className={cn(
          "flex sm:flex-row flex-col items-center gap-x-[59px] sm:gap-y-0 gap-y-6 md:max-w-3xl mx-auto",
          buttonWrapperClassName,
        )}>
        <Button
          disabled={disableButton}
          onClick={() => {
            addRecentSearch(query);
            navigate({
              to: "/search",
              search: {
                query,
              },
            });
            setShowSearch?.(false);
          }}
          className="w-full h-[54px] bg-purple01 text-white font-bold hover:bg-transparent border-purple01 border [&>div]:justify-between [&>div]:w-full px-[22px] disabled:cursor-not-allowed">
          <p>{getText("find_vtuber")}</p>
          <ArrowRight />
        </Button>
        <Button
          disabled={disableButton}
          onClick={() => {
            addRecentSearch(query);
            navigate({
              to: "/search/campaign",
              search: {
                query,
              },
            });
            setShowSearch?.(false);
          }}
          className="w-full h-[54px] bg-blue01 text-white font-bold hover:bg-transparent border border-blue01 [&>div]:justify-between [&>div]:w-full px-[22px] disabled:cursor-not-allowed">
          <p>{getText("find_campaign")}</p>
          <ArrowRight />
        </Button>
      </section>
    </div>
  );
};
