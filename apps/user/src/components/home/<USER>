import { timestampDate } from "@bufbuild/protobuf/wkt";
import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Campaign } from "@vtuber/services/campaigns";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Card, CardContent, CardHeader } from "@vtuber/ui/components/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@vtuber/ui/components/carousel";
import { Container } from "@vtuber/ui/components/container";
import { ContentHeading } from "@vtuber/ui/components/content-heading";
import { Image } from "@vtuber/ui/components/image";
import { Progress } from "@vtuber/ui/components/progress";
import { RemainingDays } from "@vtuber/ui/components/remaining-days";
import { DottedWithRectangleShape } from "@vtuber/ui/components/shape/dotted-with-rectangle";
import { getProgress } from "@vtuber/ui/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";

export const HomeRecommendedCampaigns = ({
  campaign,
}: {
  campaign: Campaign[];
}) => {
  const { getText } = useLanguage();

  return (
    <div className="space-y-[88px] bg-background relative">
      <div className="bg-sub w-full absolute md:h-[292px] h-[205px] bottom-0 left-0 md:translate-y-[100px] sm:translate-y-5 translate-y-14" />
      <DottedWithRectangleShape className="absolute bottom-0 md:right-20 right-5 md:translate-y-60 sm:translate-y-20 translate-y-[120px] md:h-[179px] md:w-[157px] w-[78px] h-[89px]" />
      <Container>
        <ContentHeading
          subTitle="Recommended Crowdfunding"
          title="おすすめクラウドファンディング"
        />
      </Container>
      <Carousel
        opts={{
          dragFree: true,
        }}>
        <CarouselContent className="md:gap-x-[60px] sm:gap-x-5 gap-x-0">
          {campaign.map((c) => {
            const progress = getProgress(c.totalBudget, c.totalRaised);
            return (
              <CarouselItem
                key={c.id}
                className="md:basis-[30%] sm:basis-[55%] basis-[80%]">
                <Link
                  to="/campaign/$id"
                  params={{
                    id: c.slug,
                  }}>
                  <Card className="rounded-[5px] p-0 bg-background">
                    <CardHeader className="p-0">
                      <AspectRatio ratio={16 / 9}>
                        <Image
                          src={c.thumbnail}
                          alt={c.name}
                        />
                      </AspectRatio>
                    </CardHeader>
                    <CardContent className="p-6 space-y-4">
                      <h3 className="md:text-[22px] text-base font-bold text-font line-clamp-2">
                        {c.shortDescription}
                      </h3>
                      <div className="bg-[#3E3B59] rounded-[3px] px-[10px] py-2 w-max font-medium text-font">
                        {timestampDate(c.startDate!).toLocaleDateString()} -{" "}
                        {timestampDate(c.endDate!).toLocaleDateString()}
                      </div>
                      <div className="flex items-center justify-between">
                        <h6 className="font-medium md:text-[43px] text-[29px]">
                          {c.totalBudget.toLocaleString()}{" "}
                          <span className="md:text-xl text-xs">
                            {getText("yen")}
                          </span>
                        </h6>
                        <RemainingDays
                          daysClassname="md:text-[34px] text-[23px]"
                          className="font-medium md:text-[17px] text-xs"
                          endDate={c.endDate!}
                          startDate={c.startDate!}
                        />
                      </div>
                      <div className="space-y-[5px]">
                        <div className="flex items-center justify-between text-font md:text-base text-xs">
                          <p>{getText("progress")}</p>
                          <p>{progress}%</p>
                        </div>
                        <Progress
                          value={progress}
                          className="h-[10px]"
                        />
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              </CarouselItem>
            );
          })}
        </CarouselContent>
        <CarouselPrevious
          className="md:size-[95px] sm:size-[65px] size-[44px] border border-[#E4E4E4] bg-[#5F5D75] md:left-[29%] left-4 -translate-x-0"
          style={{
            filter: "drop-shadow(0px 0px 15px rgba(255,255,255,0.25))",
          }}>
          <ChevronLeft className="text-[#E4E4E4] size-[22px]" />
        </CarouselPrevious>
        <CarouselNext
          className="md:size-[95px] sm:size-[65px] size-[44px] border border-[#E4E4E4] bg-[#5F5D75] md:right-[31%] right-4 -translate-x-0"
          style={{
            filter: "drop-shadow(0px 0px 15px rgba(255,255,255,0.25))",
          }}>
          <ChevronRight className="text-[#E4E4E4] size-[22px]" />
        </CarouselNext>
      </Carousel>
    </div>
  );
};
