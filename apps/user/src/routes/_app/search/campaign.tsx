import {
  createFileRoute,
  getRoute<PERSON><PERSON>,
  useNavigate,
} from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { DisplayTag } from "@vtuber/ui/components/display-tag";
import { CampaignCard } from "~/components/campaign/campaign-card";
import { LoadMoreButton } from "~/components/load-more-button";

export const Route = createFileRoute("/_app/search/campaign")({
  component: RouteComponent,
});

function RouteComponent() {
  const Route = getRouteApi("/_app/search");
  const { campaigns } = Route.useLoaderData();
  const { query } = Route.useSearch();
  const navigate = useNavigate();
  const { language } = useLanguage();

  return (
    <div className="sm:space-y-[71px] space-y-8">
      <section className="space-y-6">
        <DisplayTag
          type="underlined"
          className="capitalize"
          text={"crowdfunding"}
        />
        {campaigns?.paginationDetails?.currentPage ===
        campaigns?.paginationDetails?.nextPage ? null : (
          <p className="font-medium text-font">
            {language === "en" ? "showing" : ""}{" "}
            <span className="text-tertiary">1-20</span>
            {language === "ja" ? "件表示" : ""} /{" "}
            {campaigns?.paginationDetails?.totalItems}
            {language === "ja" ? "件中" : " results"}
          </p>
        )}
      </section>
      <div className="grid lg:grid-cols-3 xs:grid-cols-2 grid-cols-1 gap-y-[80px] xs:gap-x-10">
        {campaigns?.data.map((c) => (
          <CampaignCard
            key={c.id}
            campaign={c}
          />
        ))}
      </div>
      <LoadMoreButton
        itemsLength={campaigns?.data?.length || 0}
        totalItems={campaigns?.paginationDetails?.totalItems || 0}
        onClick={() => {
          navigate({
            to: "/search/campaign",

            search: (prev) => ({
              ...prev,
              query,
              size: (prev.size || 15) + 3,
            }),
            resetScroll: false,
          });
        }}
      />
    </div>
  );
}
