import {
  createFileRoute,
  Outlet,
  useLocation,
  useNavigate,
} from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { campaignClient, vtuberProfilesClient } from "@vtuber/services/client";
import { But<PERSON> } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { cn } from "@vtuber/ui/lib/utils";
import z from "zod";
import { CallToAction } from "~/components/layout/call-to-action";
import { PageTitle } from "~/components/layout/page-title";
import { SearchBar } from "~/components/layout/search-bar";
import { SearchNoResultsMessage } from "~/components/layout/search-no-results-message";
import { validatePagination } from "~/data/constants";

export const Route = createFileRoute("/_app/search")({
  component: RouteComponent,
  validateSearch: validatePagination.extend({
    query: z.string(),
    categoryId: z.string().optional(),
  }),
  loaderDeps: ({ search }) => search,
  loader: async ({ deps }) => {
    const { query, size } = deps;

    const [campaigns] = await campaignClient.searchCampaign({
      query,
      pagination: {
        size: size || 15,
        order: "DESC",
        sort: "created_at",
      },
    });
    const [vtubers] = await vtuberProfilesClient.searchVtuberProfiles({
      query,
      pagination: {
        size: size || 20,
        order: "DESC",
        sort: "created_at",
      },
    });

    return { campaigns, vtubers };
  },
});

function RouteComponent() {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { campaigns, vtubers } = Route.useLoaderData();
  const { getText, language } = useLanguage();
  const { query, categoryId } = Route.useSearch();
  const isDataAvailable =
    (campaigns?.data && campaigns.data.length > 0) ||
    (vtubers?.data && vtubers.data.length > 0);

  return (
    <div className="pt-20">
      <Container className="sm:pb-40 pb-20">
        <PageTitle title="site_search" />
        <div
          className={cn(
            "pt-20",
            isDataAvailable
              ? "sm:space-y-[71px] space-y-20"
              : "sm:space-y-[68px] space-y-14",
          )}>
          <SearchBar
            className="lg:max-w-[1000px] mx-auto"
            searchQuery={query}
            categoryId={categoryId}
          />
          {isDataAvailable ? (
            <div className="sm:space-y-[71px] space-y-14">
              <div className="sm:space-y-[71px] space-y-8">
                <h3 className="font-bold text-[28px] text-font">
                  {language === "ja"
                    ? `"${categoryId || query}" の検索結果`
                    : `Search results for "${categoryId || query}"`}
                </h3>
                <section className="flex sm:items-center items-start sm:gap-x-[59px] sm:flex-row flex-col sm:gap-y-0 gap-y-[27px]">
                  <Button
                    onClick={() => {
                      navigate({
                        to: "/search",
                        search: {
                          query: query,
                        },
                        resetScroll: false,
                      });
                    }}
                    variant={
                      pathname === "/search" ? "purple" : "white-outline"
                    }
                    className="h-[58px] px-[46px] text-lg font-medium">
                    {language === "ja" ? (
                      <p>
                        VTuber{" "}
                        <span className="text-[29px]">
                          {vtubers?.paginationDetails?.totalItems}
                        </span>
                        件
                      </p>
                    ) : (
                      <p>
                        <span className="text-[29px]">
                          {vtubers?.paginationDetails?.totalItems}
                        </span>
                        VTubers
                      </p>
                    )}
                  </Button>
                  <Button
                    onClick={() => {
                      navigate({
                        to: "/search/campaign",
                        search: { query },
                        resetScroll: false,
                      });
                    }}
                    variant={"white-outline"}
                    className={cn(
                      "h-[58px] px-[46px] text-lg font-medium",
                      pathname === "/search/campaign"
                        ? "bg-blue01 hover:bg-transparent hover:text-white border-blue01"
                        : "",
                    )}>
                    {language === "ja" ? (
                      <p>
                        クラウドファンディング{" "}
                        <span className="text-[29px]">
                          {campaigns?.paginationDetails?.totalItems}
                        </span>
                        件
                      </p>
                    ) : (
                      <p>
                        <span className="text-[29px]">
                          {campaigns?.paginationDetails?.totalItems}
                        </span>
                        Campaigns
                      </p>
                    )}
                  </Button>
                </section>
              </div>

              <Outlet />
            </div>
          ) : (
            <SearchNoResultsMessage
              query={query}
              categoryId={categoryId}
            />
          )}
        </div>
        {/* <SearchNoResultsMessage /> */}
      </Container>
      <CallToAction />
    </div>
  );
}
