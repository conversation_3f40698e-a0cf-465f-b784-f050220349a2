import {
  createFileRoute,
  get<PERSON>out<PERSON><PERSON><PERSON>,
  <PERSON>,
  useNavigate,
} from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { DisplayTag } from "@vtuber/ui/components/display-tag";
import { Image } from "@vtuber/ui/components/image";
import { LoadMoreButton } from "~/components/load-more-button";

export const Route = createFileRoute("/_app/search/")({
  component: RouteComponent,
});

function RouteComponent() {
  const Route = getRouteApi("/_app/search");
  const { vtubers } = Route.useLoaderData();
  const { query } = Route.useSearch();
  const navigate = useNavigate();
  const { language } = useLanguage();
  return (
    <div className="sm:space-y-[71px] space-y-8">
      <section className="space-y-6">
        <DisplayTag
          type="underlined"
          className="capitalize"
          text={"Vtuber"}
        />
        <p className="font-medium text-font">
          {language === "en" ? "showing" : ""}{" "}
          <span className="text-tertiary">1-20</span>
          {language === "ja" ? "件表示" : ""} /{" "}
          {vtubers?.paginationDetails?.totalItems}
          {language === "ja" ? "件中" : " results"}
        </p>
      </section>

      <div className="grid lg:grid-cols-4 sm:grid-cols-3 grid-cols-2 sm:gap-y-8 gap-y-6 sm:gap-x-10 gap-x-[13px]">
        {vtubers?.data.map((v) => (
          <Link
            to="/vtuber/$id"
            params={{
              id: v.username,
            }}
            className="space-y-2 group block"
            key={v.id}>
            <AspectRatio
              ratio={16 / 9}
              className="overflow-hidden rounded-[2px]">
              <Image
                src={v.image}
                alt={v.displayName}
                className="group-hover:scale-105 transition-transform duration-300"
              />
            </AspectRatio>
            <p className="text-font sm:font-bold font-medium sm:text-lg group-hover:text-tertiary">
              {v.displayName}
            </p>
          </Link>
        ))}
      </div>
      <LoadMoreButton
        itemsLength={vtubers?.data?.length || 0}
        totalItems={vtubers?.paginationDetails?.totalItems || 0}
        onClick={() => {
          navigate({
            to: "/search",

            search: (prev) => ({
              ...prev,
              query,
              size: (prev.size || 20) + 4,
            }),
            resetScroll: false,
          });
        }}
      />
    </div>
  );
}
