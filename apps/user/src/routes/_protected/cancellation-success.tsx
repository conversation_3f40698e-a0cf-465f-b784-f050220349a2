import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { buttonVariants } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { cn } from "@vtuber/ui/lib/utils";
import { CallToAction } from "~/components/layout/call-to-action";
import { seo } from "~/utils/seo";

export const Route = createFileRoute("/_protected/cancellation-success")({
  component: RouteComponent,
  head: () => ({
    meta: seo({
      title: "V-SAI | Cancellation Success",
    }),
  }),
});

function RouteComponent() {
  const { getText } = useLanguage();
  return (
    <div>
      <Container className="text-font flex flex-col items-center py-40 gap-y-16">
        <h1 className="text-center font-bold sm:text-[40px] text-2xl w-fit mx-auto bg-clip-text text-transparent bg-gradient-text">
          退会手続きを完了しました
        </h1>
        <section className="gap-y-[56px] flex flex-col items-center">
          <div className="sm:text-lg text-center leading-[180%]">
            <p>V祭をご利用いただきありがとうございました。</p>
            <p>退会手続き完了メールを送信しておりますので、ご確認ください。</p>
            <p>またのご利用をお待ちしております。</p>
          </div>
          <Link
            to="/"
            className={cn(
              buttonVariants({
                variant: "outline",
              }),
              "font-bold rounded-full h-[60px] sm:w-[424px] w-full mx-auto",
            )}>
            {getText("back_to_top")}
          </Link>
        </section>
      </Container>
      <CallToAction />
    </div>
  );
}
