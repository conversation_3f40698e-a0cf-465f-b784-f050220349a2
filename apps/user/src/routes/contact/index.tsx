import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, Link, useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { ContactService } from "@vtuber/services/cms";
import { Button } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { Form } from "@vtuber/ui/components/form";
import { SelectInput } from "@vtuber/ui/components/form-inputs/select-input";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { InputBadge } from "@vtuber/ui/components/input-badge";
import { Spinner } from "@vtuber/ui/components/spinner";
import { successToast } from "@vtuber/ui/components/toaster";
import { ArrowRight, Triangle } from "lucide-react";
import { useForm } from "react-hook-form";
import { ContactFooter } from "~/components/contact-footer";
import { PageTitle } from "~/components/layout/page-title";

export const Route = createFileRoute("/contact/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { getText, language } = useLanguage();
  const navigate = useNavigate();
  const form = useForm({
    defaultValues: {
      companyName: "",
      name: "",
      projectUrl: "",
      inquiryType: "",
      email: "",
      emailConfirm: "",
      phoneNo: "",
      inquiryDetails: "",
    },
  });

  const { mutateAsync, isPending } = useMutation(
    ContactService.method.sendAdminContactEmail,
    {
      onError: (err) => {
        handleConnectError(err, form);
      },
      onSuccess: (data) => {
        successToast(data.message);
        navigate({
          to: "/contact/thanks",
        });
      },
    },
  );

  return (
    <Container className="pt-20">
      <PageTitle title="contact_page_title" />
      <div className="md:max-w-[824px] mx-auto w-full grid gap-y-[35px] text-font sm:pt-20 pt-10">
        {language === "ja" ? (
          <h3 className="sm:text-lg text-sm leading-[180%]">
            リターン内容や配送確認、プロジェクトに関するご質問は、以下のフォームよりご利用ください。ご不明な点がありましたら、お問い合わせ前に{" "}
            「
            <Link
              to="/faq"
              className="text-blue01 hover:underline">
              よくある質問
            </Link>
            」 をご確認ください。
          </h3>
        ) : (
          <h3 className="sm:text-lg text-sm leading-[180%]">
            If you have any questions about reward details, delivery
            confirmation, or the project, please use the form below. If you have
            any questions, please check the "
            <Link
              to="/faq"
              className="text-blue01 hover:underline">
              FAQ
            </Link>
            " before contacting us.
          </h3>
        )}
        Frequently Asked Questions
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(async (v) => {
              if (v.email !== v.emailConfirm) {
                form.setError("emailConfirm", {
                  message: getText("email_confirmation_error"),
                });
                return;
              }
              await mutateAsync(v);
            })}
            className="gap-y-[72px] grid">
            <div className="grid gap-y-[35px]">
              <div className="space-y-[10px]">
                <TextInput
                  control={form.control}
                  wrapperClassName="gap-y-[10px]"
                  name="companyName"
                  className="rounded-xs h-[50px] placeholder:text-[#505050]"
                  placeholder={getText("company_name_placeholder")}
                  label={
                    <div className="flex items-center gap-2">
                      <p className="font-bold text-font">
                        {getText("company_name")}
                      </p>
                      <InputBadge />
                    </div>
                  }
                />
                <ul className="list-disc pl-5 text-sm space-y-1">
                  <li>{getText("company_name_desc_1")}</li>
                  <li>{getText("company_name_desc_2")}</li>
                </ul>
              </div>
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder={getText("project_contact_name_placeholder")}
                name="name"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">{getText("name")}</p>
                    <InputBadge />
                  </div>
                }
              />
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder={getText("project_url_placeholder")}
                name="projectUrl"
                type="url"
                inputMode="url"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">
                      {getText("project_url")}
                    </p>
                    <InputBadge type="optional" />
                  </div>
                }
              />
              <SelectInput
                Icon={Triangle}
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder={getText("inquiry_type_placeholder")}
                name="inquiryType"
                className="rounded-xs h-[50px] placeholder:text-[#505050] [&_svg]:fill-font [&_svg]:rotate-180"
                options={[
                  {
                    label: getText("about_campaign"),
                    value: getText("about_campaign"),
                  },
                  {
                    label: getText("about_rewards"),
                    value: getText("about_rewards"),
                  },
                  {
                    label: getText("about_purchasing_plan"),
                    value: getText("about_purchasing_plan"),
                  },
                  {
                    label: getText("others"),
                    value: getText("others"),
                  },
                ]}
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">
                      {getText("inquiry_type")}
                    </p>
                    <InputBadge />
                  </div>
                }
              />
              <div className="space-y-[10px]">
                <TextInput
                  control={form.control}
                  wrapperClassName="gap-y-[10px]"
                  name="email"
                  type="email"
                  inputMode="email"
                  className="rounded-xs h-[50px] placeholder:text-[#505050]"
                  placeholder={getText("project_contact_email_placeholder")}
                  label={
                    <div className="flex items-center gap-2">
                      <p className="font-bold text-font">
                        {getText("email_address")}
                      </p>
                      <InputBadge />
                    </div>
                  }
                />
                <ul className="list-disc pl-5 text-sm space-y-1">
                  <li>{getText("contact_email_confirmation_details_1")}</li>
                  <li>{getText("contact_email_confirmation_details_2")}</li>
                  <li>{getText("contact_email_confirmation_details_3")}</li>
                  <li>{getText("contact_email_confirmation_details_4")}</li>
                  <li>{getText("contact_email_confirmation_details_5")}</li>
                </ul>
              </div>
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                name="emailConfirm"
                type="email"
                inputMode="email"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                placeholder={getText("project_contact_email_placeholder")}
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">
                      {getText("email_confirmation")}
                    </p>
                    <InputBadge />
                  </div>
                }
              />
              <div className="space-y-[10px]">
                <TextInput
                  control={form.control}
                  wrapperClassName="gap-y-[10px]"
                  name="phoneNo"
                  type="tel"
                  inputMode="tel"
                  className="rounded-xs h-[50px] placeholder:text-[#505050]"
                  placeholder={getText("phone_number_placeholder")}
                  label={
                    <div className="flex items-center gap-2">
                      <p className="font-bold text-font">
                        {getText("phone_number")}
                      </p>
                      <InputBadge type="optional" />
                    </div>
                  }
                />
                <ul className="list-inside list-disc text-sm text-font">
                  <li>{getText("contact_phone_desc")}</li>
                </ul>
              </div>
              <TextAreaInput
                minHeight={215}
                className="rounded-xs placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">
                      {getText("inquiry_details")}
                    </p>
                    <InputBadge />
                  </div>
                }
                control={form.control}
                name="inquiryDetails"
                placeholder={getText("project_contact_inqiury_placeholder")}
              />
            </div>
            <Button
              variant={"tertiary"}
              className="h-[68px] base:w-[336px] w-full mx-auto text-white block pl-[46px] pr-[30px] justify-between">
              <div className=" flex justify-between items-center w-full">
                <p className="font-bold text-lg">{getText("send")}</p>
                {isPending ? <Spinner className="!size-8" /> : <ArrowRight />}
              </div>
            </Button>
          </form>
        </Form>
        <div className="bg-[#2C2820] sm:p-8 p-4 rounded-xs leading-[180%]">
          <h6>{getText("please_read_before_sending")}</h6>
          <ul className="list-disc text-[#CCCCCC] font-medium pl-5">
            {language === "ja" ? (
              <li>
                リターンに関するお問い合わせは、リターン実施はプロジェクトオーナー様の責務になるため、各プロジェクトページ内の「オーナーへのお問い合わせ」よりお問い合わせください。
                <Link
                  to="/terms"
                  className="hover:underline text-blue01">
                  利用規約
                </Link>
                (第11条 免責)をご確認ください。
              </li>
            ) : (
              <li>
                For inquiries regarding rewards, please contact us via "Contact
                the Owner" on each project page, as the implementation of the
                reward is the responsibility of the project owner. Please refer
                to the{" "}
                <Link
                  to="/terms"
                  className="hover:underline text-blue01">
                  Terms of Use
                </Link>{" "}
                (Article 11 Disclaimer).
              </li>
            )}
            <li>{getText("contact_readme")}</li>
            {language === "ja" ? (
              <li>
                事前に「
                <Link
                  className="text-blue01 hover:underline"
                  to="/faq">
                  よくあるご質問
                </Link>
                」をご確認ください。
              </li>
            ) : (
              <li>
                Please check the{" "}
                <Link
                  className="text-blue01 hover:underline"
                  to="/faq">
                  FAQ
                </Link>{" "}
                in advance.
              </li>
            )}
          </ul>
        </div>
      </div>
      <ContactFooter />
    </Container>
  );
}
