import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { handleConnectError } from "@vtuber/services/client";
import { ContactService } from "@vtuber/services/cms";
import { successToast } from "@vtuber/ui/components/toaster";
import z from "zod";
import { ContactFooter } from "~/components/contact-footer";
import { ProjectContactForm } from "~/components/project-contact-form";

export const Route = createFileRoute("/project/$id/contact/")({
  component: RouteComponent,
  validateSearch: z.object({
    projectName: z.string().optional(),
    mailType: z.enum(["campaign", "event"]).optional(),
  }),
});
function RouteComponent() {
  const { projectName, mailType = "campaign" } = Route.useSearch();
  const navigate = useNavigate();
  const { id } = Route.useParams();

  const campaignMail = useMutation(
    ContactService.method.sendCampaignContactEmail,
  );

  const eventMail = useMutation(ContactService.method.sendEventContactEmail);

  return (
    <div>
      <ProjectContactForm
        isPending={campaignMail.isPending || eventMail.isPending}
        projectName={projectName}
        onSubmit={(v, form) => {
          if (mailType === "campaign") {
            campaignMail
              .mutateAsync({
                id,
                ...v,
              })
              .then((res) => {
                successToast(res.message);
                navigate({
                  to: "/project/$id/contact/thanks",
                  params: {
                    id,
                  },
                });
              })
              .catch((err) => {
                handleConnectError(err, form);
              });
            return;
          }
          eventMail
            .mutateAsync({
              id,
              ...v,
            })
            .then((res) => {
              successToast(res.message);
              navigate({
                to: "/project/$id/contact/thanks",
                params: {
                  id,
                },
              });
            })
            .catch((err) => {
              handleConnectError(err, form);
            });
        }}
      />
      <ContactFooter />
    </div>
  );
}
