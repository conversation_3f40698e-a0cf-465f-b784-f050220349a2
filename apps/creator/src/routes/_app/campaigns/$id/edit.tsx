import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { campaignClient } from "@vtuber/services/client";
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  Ta<PERSON>List,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { useState } from "react";
import { z } from "zod";
import { CampaignBannerForm } from "~/components/campaigns/campaign-banner-form";
import { CampaignForm } from "~/components/campaigns/campaign-form";
import { CampaignVariantForm } from "~/components/campaigns/campaign-variant-form";

export const Route = createFileRoute(`/_app/campaigns/$id/edit`)({
  component: RouteComponent,
  validateSearch: z.object({
    tab: z.string().optional(),
  }),
  loader: async ({ params }) => {
    const [campaign] = await campaignClient.getCampaignById({
      id: params.id,
    });

    return { campaign };
  },
});

function RouteComponent() {
  const navigate = useNavigate({ from: Route.fullPath });
  const { tab } = Route.useSearch();
  const { campaign } = Route.useLoaderData();
  const { id } = Route.useParams();
  const [tabValue, setTabValue] = useState(tab || "basic_information");

  return (
    <Tabs
      className="pt-10"
      variant={"material"}
      value={tabValue}
      onValueChange={(v) => {
        setTabValue(v);
        navigate({
          search: {
            tab: v,
          },
        });
      }}>
      <TabsList className="sm:gap-x-0 gap-x-0 md:justify-start justify-center">
        <TabsTrigger
          className="px-6"
          value="basic_information">
          Basic Information
        </TabsTrigger>
        <TabsTrigger
          className="px-6"
          value="banners">
          Campaign Banners
        </TabsTrigger>
        <TabsTrigger
          className="px-6"
          value="variants">
          Campaign Variants
        </TabsTrigger>
      </TabsList>
      <TabsContent value="basic_information">
        <div className="md:max-w-4xl w-full mx-auto">
          <CampaignForm campaign={campaign?.data} />
        </div>
      </TabsContent>
      <TabsContent value="banners">
        <div className="md:max-w-4xl w-full mx-auto">
          <CampaignBannerForm
            campaign={campaign?.data}
            campaignId={id}
            queryEnabled={tabValue === "banners"}
          />
        </div>
      </TabsContent>
      <TabsContent value="variants">
        <div className="md:max-w-4xl w-full mx-auto">
          <CampaignVariantForm
            campaign={campaign?.data}
            isEditMode
            campaignId={id}
            queryEnabled={tabValue === "variants"}
          />
        </div>
      </TabsContent>
    </Tabs>
  );
}
