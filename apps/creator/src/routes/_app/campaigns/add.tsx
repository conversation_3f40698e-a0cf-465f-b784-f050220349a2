import { createFileRoute, useNavigate } from "@tanstack/react-router";
import {
  campaignBannerClient,
  campaignClient,
  campaignVariantClient,
} from "@vtuber/services/client";
import { Progress } from "@vtuber/ui/components/progress";
import {
  StepItem,
  Stepper,
  StepperContent,
} from "@vtuber/ui/components/stepper";
import { useState } from "react";
import { z } from "zod";
import { CampaignBannerForm } from "~/components/campaigns/campaign-banner-form";
import { CampaignForm } from "~/components/campaigns/campaign-form";
import { CampaignVariantForm } from "~/components/campaigns/campaign-variant-form";

export const Route = createFileRoute("/_app/campaigns/add")({
  component: RouteComponent,
  validateSearch: z.object({
    step: z.number().optional(),
    campaignId: z.string().optional(),
  }),
  loader: async ({ location }) => {
    const id = (location.search as any)?.campaignId;

    if (!id) return {};
    const [campaign] = await campaignClient.getCampaignById({
      id,
    });
    const [banners] = await campaignBannerClient.getBannerByCampaignId({
      campaignId: String(id),
    });
    const [variants] = await campaignVariantClient.getAllCampaignVariants({
      campaignId: String(id),
    });
    return { campaign, banners, variants };
  },
});

function RouteComponent() {
  return <Component />;
}

export default function Component() {
  const data = Route.useLoaderData();
  const { step, campaignId } = Route.useSearch();
  const [currentStep, setCurrentStep] = useState(step ?? 1);
  const navigate = useNavigate({ from: Route.fullPath });

  const onNext = (id?: string) => {
    setCurrentStep((prev) => prev + 1);
    navigate({
      search: {
        step: currentStep + 1,
        campaignId: id,
      },
      mask: {
        search: {
          step: undefined,
          campaignId: undefined,
        },
      },
    });
  };

  const onPrev = () => {
    setCurrentStep((prev) => prev - 1);
    navigate({
      search: (prev) => ({
        ...prev,
        step: currentStep - 1,
      }),
      mask: {
        search: {
          step: undefined,
          campaignId: undefined,
        },
      },
    });
  };

  return (
    <div className="pt-10 md:max-w-4xl w-full mx-auto">
      <Stepper
        currentStep={currentStep}
        onStepChange={setCurrentStep}>
        <div>
          <StepperContent>
            <StepItem>
              <CampaignForm
                onNext={onNext}
                campaign={data?.campaign?.data}
              />
            </StepItem>
            <StepItem>
              <CampaignBannerForm
                onNext={onNext}
                onPrev={onPrev}
                campaign={data?.campaign?.data}
                campaignId={campaignId || ""}
              />
            </StepItem>
            <StepItem>
              <CampaignVariantForm
                campaign={data?.campaign?.data}
                onPrev={onPrev}
                campaignId={campaignId || ""}
              />
            </StepItem>
          </StepperContent>
        </div>
      </Stepper>
      <section className="space-y-8 text-center">
        <Progress
          value={(currentStep / 3) * 100}
          className="bg-gray01 rounded-none"
          indicatorClassname="bg-tertiary"
        />
        <div className="text-muted-foreground text-sm font-medium tabular-nums">
          Step {currentStep} of 3
        </div>
      </section>
    </div>
  );
}
