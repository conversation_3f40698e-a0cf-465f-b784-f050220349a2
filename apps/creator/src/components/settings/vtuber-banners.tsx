import { useQuery } from "@tanstack/react-query";
import { useRouter } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { vtuberBannerClient } from "@vtuber/services/client";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Button } from "@vtuber/ui/components/button";
import { Card, CardContent } from "@vtuber/ui/components/card";
import { DeleteDialog } from "@vtuber/ui/components/DeleteDialog";
import { Image } from "@vtuber/ui/components/image";
import { MediaModal } from "@vtuber/ui/components/media-modal";
import { Skeleton } from "@vtuber/ui/components/skeleton";
import { cn } from "@vtuber/ui/lib/utils";
import { Eye, GalleryThumbnails, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { BannerPicker } from "./banner-picker";

export const VtuberBanners = ({ tabValue }: { tabValue: string }) => {
  const { session: user } = useAuth();
  const { data, isPending } = useQuery({
    queryKey: ["vtuber_banner"],
    queryFn: async () => {
      const [data] = await vtuberBannerClient.getVtuberBannerByVtuberId({
        vtuberId: String(user?.vtuber?.id!),
      });
      return data;
    },
    enabled: tabValue === "banners",
  });
  const router = useRouter();

  if (isPending)
    return (
      <Card className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl overflow-hidden">
        <CardContent className="p-8">
          <section className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
              <GalleryThumbnails className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">Your Banners</h2>
              <p className="text-gray-400"> Upload & Manage your banner</p>
            </div>
          </section>

          <div className="grid md:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-3 items-center">
            {Array.from({ length: 9 }).map((_, i) => (
              <AspectRatio
                ratio={4 / 3}
                key={i}
                className="relative group">
                <Skeleton className="size-full rounded-lg" />
              </AspectRatio>
            ))}
          </div>
        </CardContent>
      </Card>
    );

  const banners = data?.data || [];

  return (
    <Card className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl overflow-hidden">
      <CardContent className="p-8">
        <section className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
            <GalleryThumbnails className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Your Banners</h2>
            <p className="text-gray-400"> Upload & Manage your banner</p>
          </div>
        </section>

        <div
          className={cn(
            "grid md:grid-cols-3 sm:grid-cols-2 grid-cols-1 items-center",
            // banners.length > 0 && "divide-x border",
          )}>
          {banners.length > 0 &&
            banners.map((b) => (
              <AspectRatio
                ratio={16 / 9}
                key={b.id}
                className="relative group">
                <Image
                  src={b.image}
                  alt={"banner"}
                />
                <div className="flex absolute inset-0 opacity-0 group-hover:opacity-100 group-hover:backdrop-blur duration-200 transition-all ease-in-out bg-black/20 items-center justify-center">
                  <div className="flex items-center gap-3 justify-center">
                    <DeleteDialog
                      name={"Banner"}
                      onDelete={async () => {
                        const [res, err] =
                          await vtuberBannerClient.deleteVtuberBannerById({
                            id: b.id,
                          });
                        if (res) {
                          router.invalidate();
                        }
                        if (err) {
                          toast.error(err.rawMessage);
                        }
                      }}>
                      <Button
                        className="rounded-full size-12 p-0"
                        variant={"destructive"}>
                        <Trash2 />
                      </Button>
                    </DeleteDialog>
                    <MediaModal
                      media={[b.image]}
                      mediaType="picture"
                      title="vtuber banner">
                      <Button
                        variant={"accent"}
                        className="rounded-full size-12 p-0">
                        <Eye />
                      </Button>
                    </MediaModal>
                  </div>
                </div>
              </AspectRatio>
            ))}

          <BannerPicker length={banners.length} />
        </div>
      </CardContent>
    </Card>
  );
};
