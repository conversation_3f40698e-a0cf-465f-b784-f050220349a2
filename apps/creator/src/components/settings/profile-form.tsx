import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import {
  handleConnectError,
  OmitTypeName,
  uploadFile,
} from "@vtuber/services/client";
import { VtuberCategory } from "@vtuber/services/taxonomy";
import {
  UpdateVtuberProfileRequest,
  VtuberProfilesService,
} from "@vtuber/services/vtubers";
import { Button } from "@vtuber/ui/components/button";
import { Card, CardContent } from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { FileInput } from "@vtuber/ui/components/form-inputs/file-input";
import { MultiSelectInput } from "@vtuber/ui/components/form-inputs/multi-select-input";
import { SwitchInput } from "@vtuber/ui/components/form-inputs/switch-input";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { DiscordIcon } from "@vtuber/ui/components/icons/discord-icon";
import { InstagramIcon } from "@vtuber/ui/components/icons/instagram-icon";
import { TikTokIcon } from "@vtuber/ui/components/icons/tittok-icon";
import { TwitchIcon } from "@vtuber/ui/components/icons/twitch-icon";
import { XIcon } from "@vtuber/ui/components/icons/x-icon";
import { YoutubeIcon } from "@vtuber/ui/components/icons/youtube-icon";
import { Image } from "@vtuber/ui/components/image";
import { Label } from "@vtuber/ui/components/label";
import { MediaModal } from "@vtuber/ui/components/media-modal";
import { Separator } from "@vtuber/ui/components/separator";
import { Spinner } from "@vtuber/ui/components/spinner";
import { convertEmptyStringToUndefined } from "@vtuber/ui/lib/convert-empty-string-to-undefined";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import {
  Camera,
  CheckCircle,
  CircleOff,
  ImageIcon,
  Link,
  Save,
  Settings,
  Upload,
  User,
  X,
} from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

export const ProfileForm = ({
  categories,
}: {
  categories?: VtuberCategory[];
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const { session } = useAuth();
  const vtuber = session?.vtuber;
  const router = useRouter();

  const form = useForm<OmitTypeName<UpdateVtuberProfileRequest>>({
    defaultValues: {
      id: vtuber?.id,
      bannerImage: vtuber?.bannerImage,
      description: vtuber?.description,
      displayName: vtuber?.displayName,
      furigana: vtuber?.furigana,
      image: vtuber?.image,
      socialMediaLinks: vtuber?.socialMediaLinks,
      categories: vtuber?.categories,
      isPlanActive: vtuber?.isPlanActive,
    },
  });

  const planActive = form.watch("isPlanActive");

  const mutation = useMutation(
    VtuberProfilesService.method.updateVtuberProfile,
    {
      onError: (err) => {
        handleConnectError(err, form);
      },
      onSuccess: (data) => {
        router.invalidate();
        toast.success(data.message);
      },
    },
  );

  const onSubmit = form.handleSubmit(async (val) => {
    await mutation.mutateAsync({
      ...val,
      socialMediaLinks: convertEmptyStringToUndefined(
        val.socialMediaLinks || {},
      ),
    });
  });

  const vtuberImage = getCdnUrl(form.watch("image"));

  return (
    <Form {...form}>
      <form className="space-y-8">
        <Card className="bg-white/5 relative z-20 backdrop-blur-xl border border-white/10 rounded-3xl">
          <CardContent className="p-8">
            <section className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                <User className="w-5 h-5" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Basic Information</h2>
                <p className="text-muted-foreground">
                  Your profile details and bio
                </p>
              </div>
            </section>

            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <TextInput
                  variant={"muted"}
                  control={form.control}
                  name="displayName"
                  labelClassName="font-medium text-lg"
                  label="Display Name"
                  className="rounded-lg"
                  size={"lg"}
                  wrapperClassName="gap-y-2"
                />
                <TextInput
                  control={form.control}
                  name="furigana"
                  label="Furigana"
                  variant={"muted"}
                  size={"lg"}
                  labelClassName="font-medium text-lg"
                  className="rounded-lg"
                  wrapperClassName="gap-y-2"
                />
              </div>
              <MultiSelectInput
                control={form.control}
                name="categories"
                label="Categories"
                variant={"muted"}
                wrapperClassName="gap-y-2"
                size={"lg"}
                placeholder="Select categories"
                options={
                  categories?.map((c) => ({
                    label: c.name,
                    value: c.id.toString(),
                  })) || []
                }
              />
              <TextAreaInput
                control={form.control}
                name="description"
                label="Description"
                rows={4}
                labelClassName="font-medium text-lg"
                className="rounded-lg"
                wrapperClassName="gap-y-2"
                variant={"muted"}
              />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-white/5 relative z-20 backdrop-blur-xl border border-white/10 rounded-3xl">
          <CardContent className="p-8">
            <section className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 rounded-xl bg-gradient-3 flex items-center justify-center">
                <Settings className="w-5 h-5" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Settings</h2>
                <p className="text-muted-foreground">
                  Manage your plan settings
                </p>
              </div>
            </section>
            <div className="items-center justify-between flex overflow-hidden relative">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  {planActive ? (
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={"active"}
                        initial={{ opacity: 0, scale: 0.5 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.5 }}
                        transition={{ duration: 0.1 }}>
                        <CheckCircle className="size-6 text-green-600" />
                      </motion.div>
                    </AnimatePresence>
                  ) : (
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={"inactive"}
                        initial={{ opacity: 0, scale: 0.5 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.5 }}
                        transition={{ duration: 0.1 }}>
                        <CircleOff className="size-6 text-destructive" />
                      </motion.div>
                    </AnimatePresence>
                  )}
                  <p className="font-medium">
                    {planActive
                      ? "Your plan is active"
                      : "Your plan is not active"}
                  </p>
                </div>
                <p className="text-sm text-muted-foreground">
                  {planActive
                    ? "User can support your content"
                    : "User won't be able to support your content"}
                </p>
              </div>
              <Label
                className="absolute inset-0"
                htmlFor="isPlanActive"
              />
              <SwitchInput
                size="lg"
                name="isPlanActive"
                control={form.control}
              />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl overflow-hidden">
          <CardContent className="p-8">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                <ImageIcon className="w-5 h-5" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Profile Media</h2>
                <p className="text-muted-foreground">
                  Upload your profile picture and banner
                </p>
              </div>
            </div>

            <div className="space-y-8">
              <div className="space-y-4">
                <Label className="text-gray-300 font-medium text-lg">
                  Profile Picture
                </Label>
                <div className="flex items-center gap-6">
                  <MediaModal
                    media={[vtuberImage]}
                    mediaType="picture"
                    title={vtuber?.displayName || "Profile Picture"}>
                    <button
                      type="button"
                      className="relative group size-24 overflow-hidden">
                      <Image
                        src={vtuberImage}
                        alt={vtuber?.displayName}
                        width={96}
                        height={96}
                        className="object-cover rounded-2xl border-sub border-2 size-full"
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-all duration-300 rounded-2xl flex items-center justify-center">
                        <Camera className="w-6 h-6 text-white" />
                      </div>
                    </button>
                  </MediaModal>
                  <div className="space-y-3">
                    <div className="flex gap-3">
                      <Label
                        htmlFor="image"
                        className="bg-white/10 flex items-center gap-2 hover:bg-white/20 text-white border border-white/20 rounded-xl px-4 py-2 transition-all duration-300">
                        {isUploading ? (
                          <Spinner className="!size-4" />
                        ) : (
                          <Upload className="size-4" />
                        )}
                        Upload New
                      </Label>
                      <input
                        type="file"
                        id="image"
                        accept="image/*"
                        className="hidden"
                        onChange={async (e) => {
                          setIsUploading(true);
                          const file = e.target.files?.[0];
                          if (file) {
                            const url = await uploadFile(file);
                            if (!!url) {
                              form.setValue("image", url);
                            }
                          }
                          setIsUploading(false);
                        }}
                      />
                      <Button
                        type="button"
                        onClick={() => {
                          form.setValue("image", "");
                        }}
                        disabled={!vtuberImage || isUploading}
                        variant="outline"
                        className="border-red-500/50 text-red-400 hover:bg-red-500/10 rounded-xl px-4 py-2 transition-all duration-300">
                        <X className="w-4 h-4 mr-2" />
                        Remove
                      </Button>
                    </div>
                    <p className="text-muted-foreground text-sm">
                      Recommended size: 1080x1080px
                    </p>
                  </div>
                </div>
              </div>

              <Separator className="bg-white/10" />
              <FileInput
                control={form.control}
                name="bannerImage"
                wrapperClassName="space-y-4"
                label="Banner Image"
                labelClassName="font-medium text-lg"
                className="bg-sub/40 border-sub border-2 rounded-2xl"
                defaultImage={vtuber?.bannerImage}
                fileUploadDescription="Recommended size: 1920x1080px"
              />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl overflow-hidden">
          <CardContent className="p-8">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-green-500 to-teal-500 flex items-center justify-center">
                <Link className="w-5 h-5" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Social Links</h2>
                <p className="text-muted-foreground">
                  Connect your social media accounts
                </p>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6 items-center">
              <TextInput
                control={form.control}
                name="socialMediaLinks.twitter"
                placeholder="https://twitter.com/..."
                className="rounded-lg pl-12"
                leftIcon={
                  <div className="size-7 from-black/50 to-black rounded-lg bg-gradient-to-r flex items-center justify-center flex-shrink-0">
                    <XIcon className="size-4" />
                  </div>
                }
                inputMode="url"
                type="url"
                variant={"muted"}
                size={"lg"}
              />
              <TextInput
                control={form.control}
                name="socialMediaLinks.instagram"
                placeholder="https://instagram.com/..."
                size={"lg"}
                type="url"
                variant={"muted"}
                className="rounded-lg pl-12"
                leftIcon={
                  <div className="size-7 from-pink-500 to-rose-500 rounded-lg bg-gradient-to-r flex items-center justify-center flex-shrink-0">
                    <InstagramIcon className="size-4" />
                  </div>
                }
                inputMode="url"
              />
              <TextInput
                control={form.control}
                name="socialMediaLinks.discord"
                placeholder="https://discord.com/..."
                className="rounded-lg pl-12"
                type="url"
                leftIcon={
                  <div className="size-7 from-[#7289da] to-[#4e5d94] rounded-lg bg-gradient-to-r flex items-center justify-center flex-shrink-0">
                    <DiscordIcon className="size-4" />
                  </div>
                }
                inputMode="url"
                variant={"muted"}
                size={"lg"}
              />
              <TextInput
                control={form.control}
                name="socialMediaLinks.tiktok"
                size={"lg"}
                variant={"muted"}
                type="url"
                className="rounded-lg pl-12"
                placeholder="https://tiktok.com/..."
                leftIcon={
                  <div className="size-7 to-[#040404] from-[#fe2858] rounded-lg bg-gradient-to-r flex items-center justify-center flex-shrink-0">
                    <TikTokIcon className="size-4" />
                  </div>
                }
                inputMode="url"
              />
              <TextInput
                control={form.control}
                name="socialMediaLinks.twitch"
                placeholder="https://twitch.com/..."
                type="url"
                className="rounded-lg pl-12"
                leftIcon={
                  <div className="size-7 from-purple-500 to-purple-600 rounded-lg bg-gradient-to-r flex items-center justify-center flex-shrink-0">
                    <TwitchIcon className="size-4" />
                  </div>
                }
                inputMode="url"
                variant={"muted"}
                size={"lg"}
              />
              <TextInput
                control={form.control}
                name="socialMediaLinks.youtube"
                size={"lg"}
                placeholder="https://youtube.com/..."
                type="url"
                variant={"muted"}
                className="rounded-lg pl-12"
                leftIcon={
                  <div className="size-7 from-red-500 to-red-600 rounded-lg [&_path]:fill-white bg-gradient-to-r flex items-center justify-center flex-shrink-0">
                    <YoutubeIcon className="size-4" />
                  </div>
                }
                inputMode="url"
              />
            </div>
          </CardContent>
        </Card>
        <div className="flex justify-end">
          <Button
            className="rounded-xl bg-gradient-to-tr from-violet-600 to-cyan-600 hover:from-violet-700 hover:to-cyan-700 text-white w-full"
            size={"xl"}
            onClick={onSubmit}>
            <div className="flex items-center gap-2">
              {mutation.isPending ? (
                <Spinner className="!size-6" />
              ) : (
                <Save className="size-4" />
              )}
              Save Changes
            </div>
          </Button>
        </div>
      </form>
    </Form>
  );
};
