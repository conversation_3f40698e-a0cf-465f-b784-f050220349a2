import { useMutation } from "@connectrpc/connect-query";
import { useCategories } from "@vtuber/auth/hooks";
import {
  AddCampaignRequest,
  CampaignService,
  GetCampaignById,
} from "@vtuber/services/campaigns";
import { handleConnectError, OmitTypeName } from "@vtuber/services/client";

import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { DateInput } from "@vtuber/ui/components/form-inputs/date-input";
import { FileInput } from "@vtuber/ui/components/form-inputs/file-input";
import { HtmlInput } from "@vtuber/ui/components/form-inputs/html-input";
import { MultiSelectInput } from "@vtuber/ui/components/form-inputs/multi-select-input";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@vtuber/ui/components/hover-card";
import { Image } from "@vtuber/ui/components/image";
import { Separator } from "@vtuber/ui/components/separator";
import { Spinner } from "@vtuber/ui/components/spinner";
import { convertEmptyStringToUndefined } from "@vtuber/ui/lib/convert-empty-string-to-undefined";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import {
  ArrowRight,
  Bookmark,
  FileText,
  HelpCircle,
  ImageIcon,
  InfoIcon,
  Link,
  Save,
} from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

interface Props {
  campaign?: GetCampaignById;
  onNext?: (campaign?: string) => void;
}

export function CampaignForm({ campaign, onNext }: Props) {
  const router = useRouter();
  const { getText } = useLanguage();
  const form = useForm<OmitTypeName<AddCampaignRequest>>({
    defaultValues: {
      description: campaign?.description,
      endDate: campaign?.endDate,
      name: campaign?.name,
      promotionalMessage: campaign?.promotionalMessage,
      shortDescription: campaign?.shortDescription,
      socialMediaLinks: campaign?.socialMediaLinks,
      startDate: campaign?.startDate,
      thumbnail: campaign?.thumbnail,
      totalBudget: campaign?.totalBudget,
      categories: campaign?.categories,
    },
  });

  const { categories } = useCategories();
  const [isUploading, setIsUploading] = useState(false);

  const addMutation = useMutation(CampaignService.method.addCampaign, {
    onError: (err) => {
      handleConnectError(err, form);
    },
    onSuccess: (data) => {
      toast.success("Campaign created successfully");
      onNext?.(data.data?.id?.toString());
    },
  });

  const updateMutation = useMutation(
    CampaignService.method.updateCampaignById,
    {
      onError: (err) => {
        handleConnectError(err, form);
      },
      onSuccess: (data) => {
        toast.success(data.message);
        router.invalidate();
        router.navigate({
          to: "/campaigns",
        });
      },
    },
  );

  const onSubmit = form.handleSubmit((val) => {
    if (campaign) {
      updateMutation.mutateAsync({
        ...val,
        id: campaign.id,
        socialMediaLinks: convertEmptyStringToUndefined(
          val.socialMediaLinks || {},
        ),
      });

      return;
    }
    addMutation.mutateAsync({
      ...val,
      socialMediaLinks: convertEmptyStringToUndefined(
        val.socialMediaLinks || {},
      ),
    });
  });
  const isPending = addMutation.isPending || updateMutation.isPending;
  return (
    <section className="py-10 space-y-10">
      <Form {...form}>
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <InfoIcon className="size-5 mt-1" /> Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <TextInput
                wrapperClassName="space-y-2"
                variant={"muted"}
                control={form.control}
                name="name"
                label={getText("CAMPAIGN_NAME")}
                placeholder={getText("CAMPAIGN_NAME")}
              />
              <div className="grid md:grid-cols-2 grid-cols-1 items-center gap-6">
                <TextInput
                  control={form.control}
                  name="totalBudget"
                  leftIcon={
                    <span className="text-muted-foreground">
                      {getText("yen")}
                    </span>
                  }
                  label={getText("TOTAL_BUDGET")}
                  placeholder={getText("TOTAL_BUDGET")}
                  type="number"
                  wrapperClassName="space-y-2"
                  variant={"muted"}
                />
                <MultiSelectInput
                  label="Categories"
                  control={form.control}
                  name="categories"
                  variant={"muted"}
                  wrapperClassName="space-y-2"
                  options={categories.map((category) => ({
                    label: category.name,
                    value: category.id.toString(),
                  }))}
                />
                <DateInput
                  wrapperClassName="space-y-2"
                  className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 border focus-visible:ring-1 focus-visible:ring-white rounded-md"
                  minDate={
                    new Date(new Date().setDate(new Date().getDate() + 1))
                  }
                  control={form.control}
                  name="startDate"
                  label={getText("START_DATE")}
                />
                <DateInput
                  wrapperClassName="space-y-2"
                  className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 border focus-visible:ring-1 focus-visible:ring-white rounded-md"
                  minDate={
                    new Date(new Date().setDate(new Date().getDate() + 1))
                  }
                  control={form.control}
                  name="endDate"
                  label={getText("END_DATE")}
                />
              </div>

              <TextAreaInput
                wrapperClassName="space-y-2"
                variant={"muted"}
                control={form.control}
                name="promotionalMessage"
                label={getText("Promotional_Message")}
              />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="items-center gap-2 flex text-lg">
                <ImageIcon className="w-5 h-5 mt-1" />
                {getText("ADD_THUMBNAIL")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FileInput
                onUploadingChange={setIsUploading}
                control={form.control}
                defaultImage={getCdnUrl(campaign?.thumbnail)}
                name="thumbnail"
                accept="image/*"
                fileUploadDescription="Recommended size: 1280x720px"
              />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Link className="size-5 mt-1" /> Social Links{" "}
                <span className="text-sm text-gray01">(optional)</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid sm:grid-cols-2 items-center gap-6">
                <TextInput
                  wrapperClassName="space-y-2"
                  variant={"muted"}
                  control={form.control}
                  label="Youtube Link"
                  name="socialMediaLinks.youtube"
                  type="url"
                  inputMode="url"
                />
                <TextInput
                  wrapperClassName="space-y-2"
                  variant={"muted"}
                  label="Twitter Link"
                  control={form.control}
                  name="socialMediaLinks.twitter"
                  type="url"
                  inputMode="url"
                />
                <TextInput
                  wrapperClassName="space-y-2"
                  variant={"muted"}
                  label="Tiktok Link"
                  control={form.control}
                  name="socialMediaLinks.tiktok"
                  type="url"
                  inputMode="url"
                />
                <TextInput
                  wrapperClassName="space-y-2"
                  variant={"muted"}
                  label="Twitch Link"
                  control={form.control}
                  name="socialMediaLinks.twitch"
                  type="url"
                  inputMode="url"
                />
                <TextInput
                  wrapperClassName="space-y-2"
                  variant={"muted"}
                  label="Discord Link"
                  control={form.control}
                  name="socialMediaLinks.discord"
                  type="url"
                  inputMode="url"
                />
                <TextInput
                  wrapperClassName="space-y-2"
                  variant={"muted"}
                  label="Instagram Link"
                  control={form.control}
                  name="socialMediaLinks.instagram"
                  type="url"
                  inputMode="url"
                />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <FileText className="mt-1 size-5" />{" "}
                {getText("CAMPAIGN_DESCRIPTION")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <TextAreaInput
                wrapperClassName="space-y-2"
                variant={"muted"}
                control={form.control}
                name="shortDescription"
                label={
                  <div className="flex items-center gap-2">
                    <span>{getText("short_description")}</span>
                    <HoverCard openDelay={0}>
                      <HoverCardTrigger>
                        <HelpCircle className="size-4 text-tertiary" />
                      </HoverCardTrigger>
                      <HoverCardContent className="bg-background space-y-3 p-0">
                        <div className="pb-0 p-4">
                          <h3 className="text-font font-medium leading-relaxed">
                            This will be displayed on the campaign card.
                          </h3>
                        </div>
                        <Separator />
                        <Image
                          src="https://cdn.v-sai.com/assets/campaign_card.png"
                          className="size-full object-cover"
                          alt="Campaign Card"
                        />
                      </HoverCardContent>
                    </HoverCard>
                  </div>
                }
              />
              <HtmlInput
                control={form.control}
                name="description"
                label={getText("About")}
              />
            </CardContent>
          </Card>
        </div>
      </Form>
      <Button
        variant={"success"}
        onClick={() => {
          form.clearErrors();
          onSubmit();
        }}
        disabled={isUploading || isPending}
        size={"xl"}
        className="w-full">
        {isPending ? (
          <Spinner className="!size-8" />
        ) : campaign ? (
          <Bookmark className="w-5 h-5 mr-2" />
        ) : (
          <Save className="w-5 h-5 mr-2" />
        )}
        {campaign ? getText("Update_Campaign") : getText("Create_Campaign")}
        {onNext && <ArrowRight className="ml-3 size-5" />}
      </Button>
    </section>
  );
}
