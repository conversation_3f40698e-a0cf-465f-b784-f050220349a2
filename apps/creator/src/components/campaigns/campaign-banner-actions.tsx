import { useQueryClient } from "@tanstack/react-query";
import {
  CampaignBanner,
  GetBannerByCampaignIdResponse,
  GetCampaignById,
} from "@vtuber/services/campaigns";
import { campaignBannerClient } from "@vtuber/services/client";
import { Button } from "@vtuber/ui/components/button";
import { DeleteDialog } from "@vtuber/ui/components/DeleteDialog";
import { MediaModal } from "@vtuber/ui/components/media-modal";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import { Edit, Eye, Trash2 } from "lucide-react";
import { campaignBannerQueryOptions } from "~/utils/api";
import { CampaignBannerPicker } from "./campaign-banner-picker";

interface Props {
  banner: CampaignBanner;
  campaign?: GetCampaignById;
  campaignId: string;
}

export const CampaignBannerActions = ({
  banner,
  campaign,
  campaignId,
}: Props) => {
  const queryClient = useQueryClient();
  const queryKey = campaignBannerQueryOptions(campaignId, {}).queryKey;
  return (
    <div
      className={
        "flex items-center transition-opacity duration-300 ease-in-out justify-center inset-0 absolute bg-black/30 backdrop-blur group-hover:opacity-100 opacity-0"
      }>
      <div className="flex items-center justify-center gap-x-3">
        <MediaModal
          media={[getCdnUrl(banner.image)]}
          mediaType="picture"
          title={"Campaign Banner"}>
          <Button
            className="rounded-full size-12"
            variant={"accent"}
            size={"icon"}>
            <Eye />
          </Button>
        </MediaModal>
        <CampaignBannerPicker
          asChild
          campaign={campaign}
          campaignId={String(campaignId)}
          banner={banner}>
          <Button
            variant={"accent"}
            className="rounded-full size-12"
            size={"icon"}>
            <Edit />
          </Button>
        </CampaignBannerPicker>
        <DeleteDialog
          name={"Campaign Banner"}
          onDelete={() => {
            campaignBannerClient
              .deleteCampaignBannerById({
                id: banner.id,
              })
              .then(() => {
                queryClient.setQueryData(queryKey, (old) => {
                  if (old?.data) {
                    return {
                      ...old,
                      data: old.data.filter((b) => b.id !== banner.id),
                    } as GetBannerByCampaignIdResponse;
                  }
                  return old;
                });
              });
          }}>
          <Button
            className="rounded-full size-12"
            variant={"destructive"}
            size={"icon"}>
            <Trash2 />
          </Button>
        </DeleteDialog>
      </div>
    </div>
  );
};
