import { createFileRoute, notFound } from "@tanstack/react-router";
import { staticClient } from "@vtuber/services/client";
import { buttonVariants } from "@vtuber/ui/components/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { ExternalLink } from "lucide-react";
import { UserGuideForm } from "~/components/user-guide-form";

export const Route = createFileRoute("/_app/user-guides")({
  component: RouteComponent,
  loader: async () => {
    const [userGuide, err1] = await staticClient.getAllStaticResource({
      key: "support/user",
    });
    const [vtuberGuide, err2] = await staticClient.getAllStaticResource({
      key: "support/vtuber",
    });
    const err = err1 || err2;
    if (err) {
      throw notFound({
        data: {
          message: err.rawMessage,
        },
      });
    }
    return {
      userGuide,
      vtuberGuide,
    };
  },
});

function RouteComponent() {
  const { userGuide, vtuberGuide } = Route.useLoaderData();
  return (
    <div className="pt-10">
      <div className="grid sm:grid-cols-2 grid-cols-1 gap-6">
        <Card className="min-w-52">
          <CardHeader className="text-center">
            <CardTitle>User Guide</CardTitle>
          </CardHeader>
          <CardContent>
            {userGuide.data.length > 0 ? (
              <p className="text-center text-xl font-medium block text-blue-500 underline">
                {userGuide?.data[0].value}
              </p>
            ) : (
              <UserGuideForm type="support/user" />
            )}
          </CardContent>
          {userGuide.data.length > 0 && (
            <CardFooter className="gap-6">
              <a
                href={userGuide?.data[0].value}
                target="_blank"
                rel="noopener noreferrer"
                className={buttonVariants({
                  variant: "accent",
                  size: "xl",
                  className: "w-full",
                })}>
                <ExternalLink />
                open in new tab
              </a>
              <UserGuideForm
                type="support/user"
                id={userGuide?.data[0].id}
                content={userGuide?.data[0].value}
              />
            </CardFooter>
          )}
        </Card>
        <Card className="min-w-52">
          <CardHeader className="text-center">
            <CardTitle>Vtuber Guide</CardTitle>
          </CardHeader>
          <CardContent>
            {vtuberGuide.data.length > 0 ? (
              <p className="text-center text-xl font-medium block text-blue-500 underline">
                {vtuberGuide?.data[0].value}
              </p>
            ) : (
              <UserGuideForm type="support/vtuber" />
            )}
          </CardContent>
          {vtuberGuide.data.length > 0 && (
            <CardFooter className="gap-6">
              <a
                href={vtuberGuide?.data[0].value}
                target="_blank"
                rel="noopener noreferrer"
                className={buttonVariants({
                  variant: "accent",
                  size: "xl",
                  className: "w-full",
                })}>
                <ExternalLink />
                open in new tab
              </a>
              <UserGuideForm
                type="support/vtuber"
                id={vtuberGuide?.data[0].id}
                content={vtuberGuide?.data[0].value}
              />
            </CardFooter>
          )}
        </Card>
      </div>
    </div>
  );
}
