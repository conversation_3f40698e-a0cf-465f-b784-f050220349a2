import { createFileRoute, notFound, useRouter } from "@tanstack/react-router";
import { announcementClient } from "@vtuber/services/client";
import { Image } from "@vtuber/ui/components/image";
import { MediaModal } from "@vtuber/ui/components/media-modal";
import { AnnouncementForm } from "~/components/announcements/announcement-form";
import { ToggleAnnouncementConfirmation } from "~/components/announcements/toggle-announcement-confirmation";

export const Route = createFileRoute("/_app/announcements/")({
  component: RouteComponent,
  loader: async () => {
    const [res, err] = await announcementClient.getAnnouncement({});
    if (err) {
      throw notFound({
        data: {
          message: err.rawMessage,
        },
      });
    }
    return res.data;
  },
});

function RouteComponent() {
  const router = useRouter();
  const data = Route.useLoaderData();

  if (!data)
    return (
      <div className="min-h-[80dvh] flex items-center justify-center">
        <div className="flex flex-col items-center gap-y-6">
          <h3 className="text-3xl font-semibold">There is no Announcement </h3>
          <AnnouncementForm />
        </div>
      </div>
    );

  return (
    <div className="space-y-10">
      <div className="h-[50vh] relative">
        <div className="absolute z-20 top-5 right-5 flex items-center gap-x-6">
          <ToggleAnnouncementConfirmation data={data} />
          <AnnouncementForm data={data} />
        </div>
        <MediaModal
          media={[data?.image]}
          mediaType="picture"
          className="size-full"
          title={data?.content}>
          <Image
            alt={data?.content}
            className="object-contain size-full"
            src={data?.image}
          />
        </MediaModal>
      </div>
      <p>{data?.content}</p>
    </div>
  );
}
