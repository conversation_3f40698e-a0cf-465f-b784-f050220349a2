// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: campaigns/v1/campaigns.proto

package campaignsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	v11 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Subscription struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Id                   string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Image                string                 `protobuf:"bytes,4,opt,name=image,proto3" json:"image,omitempty"`
	Comment              *string                `protobuf:"bytes,5,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
	CreatedAt            *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	CampaignId           string                 `protobuf:"bytes,7,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	Amount               int32                  `protobuf:"varint,8,opt,name=amount,proto3" json:"amount,omitempty"`
	CampaignVariantId    string                 `protobuf:"bytes,9,opt,name=campaign_variant_id,json=campaignVariantId,proto3" json:"campaign_variant_id,omitempty"`
	CampaignVariantTitle string                 `protobuf:"bytes,10,opt,name=campaign_variant_title,json=campaignVariantTitle,proto3" json:"campaign_variant_title,omitempty"`
	CampaignVariantImage string                 `protobuf:"bytes,11,opt,name=campaign_variant_image,json=campaignVariantImage,proto3" json:"campaign_variant_image,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *Subscription) Reset() {
	*x = Subscription{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Subscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Subscription) ProtoMessage() {}

func (x *Subscription) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Subscription.ProtoReflect.Descriptor instead.
func (*Subscription) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{0}
}

func (x *Subscription) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Subscription) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Subscription) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *Subscription) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

func (x *Subscription) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Subscription) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *Subscription) GetAmount() int32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Subscription) GetCampaignVariantId() string {
	if x != nil {
		return x.CampaignVariantId
	}
	return ""
}

func (x *Subscription) GetCampaignVariantTitle() string {
	if x != nil {
		return x.CampaignVariantTitle
	}
	return ""
}

func (x *Subscription) GetCampaignVariantImage() string {
	if x != nil {
		return x.CampaignVariantImage
	}
	return ""
}

type SubscriberResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*Subscription        `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SubscriberResponse) Reset() {
	*x = SubscriberResponse{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriberResponse) ProtoMessage() {}

func (x *SubscriberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriberResponse.ProtoReflect.Descriptor instead.
func (*SubscriberResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{1}
}

func (x *SubscriberResponse) GetData() []*Subscription {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SubscriberResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type SubscriberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CampaignId    string                 `protobuf:"bytes,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty" validate:"required"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscriberRequest) Reset() {
	*x = SubscriberRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriberRequest) ProtoMessage() {}

func (x *SubscriberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriberRequest.ProtoReflect.Descriptor instead.
func (*SubscriberRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{2}
}

func (x *SubscriberRequest) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *SubscriberRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type SearchCampaignRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Query         string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty" validate:"required"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchCampaignRequest) Reset() {
	*x = SearchCampaignRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCampaignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCampaignRequest) ProtoMessage() {}

func (x *SearchCampaignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCampaignRequest.ProtoReflect.Descriptor instead.
func (*SearchCampaignRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{3}
}

func (x *SearchCampaignRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *SearchCampaignRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetMyCampaignsNameRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMyCampaignsNameRequest) Reset() {
	*x = GetMyCampaignsNameRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMyCampaignsNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyCampaignsNameRequest) ProtoMessage() {}

func (x *GetMyCampaignsNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyCampaignsNameRequest.ProtoReflect.Descriptor instead.
func (*GetMyCampaignsNameRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{4}
}

type CampaignNames struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Id            string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CampaignNames) Reset() {
	*x = CampaignNames{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CampaignNames) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignNames) ProtoMessage() {}

func (x *CampaignNames) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignNames.ProtoReflect.Descriptor instead.
func (*CampaignNames) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{5}
}

func (x *CampaignNames) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CampaignNames) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetMyCampaignNamesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*CampaignNames       `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMyCampaignNamesResponse) Reset() {
	*x = GetMyCampaignNamesResponse{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMyCampaignNamesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyCampaignNamesResponse) ProtoMessage() {}

func (x *GetMyCampaignNamesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyCampaignNamesResponse.ProtoReflect.Descriptor instead.
func (*GetMyCampaignNamesResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{6}
}

func (x *GetMyCampaignNamesResponse) GetData() []*CampaignNames {
	if x != nil {
		return x.Data
	}
	return nil
}

type AddCampaignRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Name               string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" validate:"required"`
	Description        string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	StartDate          *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate            *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	TotalBudget        int32                  `protobuf:"varint,5,opt,name=total_budget,json=totalBudget,proto3" json:"total_budget,omitempty" validate:"required"`
	Categories         []string               `protobuf:"bytes,6,rep,name=categories,proto3" json:"categories,omitempty"`
	Thumbnail          string                 `protobuf:"bytes,7,opt,name=thumbnail,proto3" json:"thumbnail,omitempty" validate:"required"`
	ShortDescription   string                 `protobuf:"bytes,10,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty" validate:"required"`
	PromotionalMessage string                 `protobuf:"bytes,11,opt,name=promotional_message,json=promotionalMessage,proto3" json:"promotional_message,omitempty" validate:"required"`
	SocialMediaLinks   *v1.SocialMediaLinks   `protobuf:"bytes,12,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AddCampaignRequest) Reset() {
	*x = AddCampaignRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCampaignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCampaignRequest) ProtoMessage() {}

func (x *AddCampaignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCampaignRequest.ProtoReflect.Descriptor instead.
func (*AddCampaignRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{7}
}

func (x *AddCampaignRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddCampaignRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AddCampaignRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *AddCampaignRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *AddCampaignRequest) GetTotalBudget() int32 {
	if x != nil {
		return x.TotalBudget
	}
	return 0
}

func (x *AddCampaignRequest) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *AddCampaignRequest) GetThumbnail() string {
	if x != nil {
		return x.Thumbnail
	}
	return ""
}

func (x *AddCampaignRequest) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *AddCampaignRequest) GetPromotionalMessage() string {
	if x != nil {
		return x.PromotionalMessage
	}
	return ""
}

func (x *AddCampaignRequest) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

type AddCampaignResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Campaign              `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCampaignResponse) Reset() {
	*x = AddCampaignResponse{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCampaignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCampaignResponse) ProtoMessage() {}

func (x *AddCampaignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCampaignResponse.ProtoReflect.Descriptor instead.
func (*AddCampaignResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{8}
}

func (x *AddCampaignResponse) GetData() *Campaign {
	if x != nil {
		return x.Data
	}
	return nil
}

type Campaign struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name               string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ShortDescription   string                 `protobuf:"bytes,3,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty"`
	StartDate          *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate            *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	TotalBudget        int32                  `protobuf:"varint,6,opt,name=total_budget,json=totalBudget,proto3" json:"total_budget,omitempty"`
	Categories         []string               `protobuf:"bytes,7,rep,name=categories,proto3" json:"categories,omitempty"`
	VtuberId           string                 `protobuf:"bytes,8,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty"`
	CreatedAt          *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Thumbnail          string                 `protobuf:"bytes,10,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
	PromotionalMessage string                 `protobuf:"bytes,11,opt,name=promotional_message,json=promotionalMessage,proto3" json:"promotional_message,omitempty"`
	TotalRaised        int32                  `protobuf:"varint,12,opt,name=total_raised,json=totalRaised,proto3" json:"total_raised,omitempty"`
	SocialMediaLinks   *v1.SocialMediaLinks   `protobuf:"bytes,13,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	Slug               string                 `protobuf:"bytes,14,opt,name=slug,proto3" json:"slug,omitempty"`
	CreatedBy          *string                `protobuf:"bytes,15,opt,name=created_by,json=createdBy,proto3,oneof" json:"created_by,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *Campaign) Reset() {
	*x = Campaign{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Campaign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Campaign) ProtoMessage() {}

func (x *Campaign) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Campaign.ProtoReflect.Descriptor instead.
func (*Campaign) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{9}
}

func (x *Campaign) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Campaign) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Campaign) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *Campaign) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *Campaign) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *Campaign) GetTotalBudget() int32 {
	if x != nil {
		return x.TotalBudget
	}
	return 0
}

func (x *Campaign) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *Campaign) GetVtuberId() string {
	if x != nil {
		return x.VtuberId
	}
	return ""
}

func (x *Campaign) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Campaign) GetThumbnail() string {
	if x != nil {
		return x.Thumbnail
	}
	return ""
}

func (x *Campaign) GetPromotionalMessage() string {
	if x != nil {
		return x.PromotionalMessage
	}
	return ""
}

func (x *Campaign) GetTotalRaised() int32 {
	if x != nil {
		return x.TotalRaised
	}
	return 0
}

func (x *Campaign) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

func (x *Campaign) GetSlug() string {
	if x != nil {
		return x.Slug
	}
	return ""
}

func (x *Campaign) GetCreatedBy() string {
	if x != nil && x.CreatedBy != nil {
		return *x.CreatedBy
	}
	return ""
}

type RelatedCampaignRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CampaignId    string                 `protobuf:"bytes,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RelatedCampaignRequest) Reset() {
	*x = RelatedCampaignRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelatedCampaignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelatedCampaignRequest) ProtoMessage() {}

func (x *RelatedCampaignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelatedCampaignRequest.ProtoReflect.Descriptor instead.
func (*RelatedCampaignRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{10}
}

func (x *RelatedCampaignRequest) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

type RelatedCampaignResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Campaign            `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RelatedCampaignResponse) Reset() {
	*x = RelatedCampaignResponse{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelatedCampaignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelatedCampaignResponse) ProtoMessage() {}

func (x *RelatedCampaignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelatedCampaignResponse.ProtoReflect.Descriptor instead.
func (*RelatedCampaignResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{11}
}

func (x *RelatedCampaignResponse) GetData() []*Campaign {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetAllCampaignsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CategoryId    *string                `protobuf:"bytes,1,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllCampaignsRequest) Reset() {
	*x = GetAllCampaignsRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllCampaignsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllCampaignsRequest) ProtoMessage() {}

func (x *GetAllCampaignsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllCampaignsRequest.ProtoReflect.Descriptor instead.
func (*GetAllCampaignsRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{12}
}

func (x *GetAllCampaignsRequest) GetCategoryId() string {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return ""
}

func (x *GetAllCampaignsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetAllCampaignsByVtuberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	VtuberId      string                 `protobuf:"bytes,2,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllCampaignsByVtuberRequest) Reset() {
	*x = GetAllCampaignsByVtuberRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllCampaignsByVtuberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllCampaignsByVtuberRequest) ProtoMessage() {}

func (x *GetAllCampaignsByVtuberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllCampaignsByVtuberRequest.ProtoReflect.Descriptor instead.
func (*GetAllCampaignsByVtuberRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{13}
}

func (x *GetAllCampaignsByVtuberRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetAllCampaignsByVtuberRequest) GetVtuberId() string {
	if x != nil {
		return x.VtuberId
	}
	return ""
}

type GetMyCampaignsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMyCampaignsRequest) Reset() {
	*x = GetMyCampaignsRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMyCampaignsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyCampaignsRequest) ProtoMessage() {}

func (x *GetMyCampaignsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyCampaignsRequest.ProtoReflect.Descriptor instead.
func (*GetMyCampaignsRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{14}
}

func (x *GetMyCampaignsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetAllCampaignsResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*Campaign            `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllCampaignsResponse) Reset() {
	*x = GetAllCampaignsResponse{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllCampaignsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllCampaignsResponse) ProtoMessage() {}

func (x *GetAllCampaignsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllCampaignsResponse.ProtoReflect.Descriptor instead.
func (*GetAllCampaignsResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{15}
}

func (x *GetAllCampaignsResponse) GetData() []*Campaign {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllCampaignsResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetCampaignByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignByIdRequest) Reset() {
	*x = GetCampaignByIdRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignByIdRequest) ProtoMessage() {}

func (x *GetCampaignByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignByIdRequest.ProtoReflect.Descriptor instead.
func (*GetCampaignByIdRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{16}
}

func (x *GetCampaignByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetCampaignById struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name               string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description        string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	ShortDescription   string                 `protobuf:"bytes,4,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty"`
	StartDate          *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate            *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	TotalBudget        int32                  `protobuf:"varint,7,opt,name=total_budget,json=totalBudget,proto3" json:"total_budget,omitempty"`
	Categories         []string               `protobuf:"bytes,8,rep,name=categories,proto3" json:"categories,omitempty"`
	Vtuber             *v11.VtuberProfile     `protobuf:"bytes,9,opt,name=vtuber,proto3" json:"vtuber,omitempty"`
	CreatedAt          *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Thumbnail          string                 `protobuf:"bytes,11,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
	Variants           []*CampaignVariantById `protobuf:"bytes,12,rep,name=variants,proto3" json:"variants,omitempty"`
	Banners            []*CampaignBanner      `protobuf:"bytes,13,rep,name=banners,proto3" json:"banners,omitempty"`
	HasLiked           bool                   `protobuf:"varint,14,opt,name=has_liked,json=hasLiked,proto3" json:"has_liked,omitempty"`
	LikeCount          int64                  `protobuf:"varint,15,opt,name=like_count,json=likeCount,proto3" json:"like_count,omitempty"`
	PromotionalMessage string                 `protobuf:"bytes,16,opt,name=promotional_message,json=promotionalMessage,proto3" json:"promotional_message,omitempty"`
	TotalRaised        int32                  `protobuf:"varint,17,opt,name=total_raised,json=totalRaised,proto3" json:"total_raised,omitempty"`
	SocialMediaLinks   *v1.SocialMediaLinks   `protobuf:"bytes,18,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	Slug               string                 `protobuf:"bytes,19,opt,name=slug,proto3" json:"slug,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetCampaignById) Reset() {
	*x = GetCampaignById{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignById) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignById) ProtoMessage() {}

func (x *GetCampaignById) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignById.ProtoReflect.Descriptor instead.
func (*GetCampaignById) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{17}
}

func (x *GetCampaignById) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetCampaignById) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetCampaignById) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GetCampaignById) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *GetCampaignById) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetCampaignById) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *GetCampaignById) GetTotalBudget() int32 {
	if x != nil {
		return x.TotalBudget
	}
	return 0
}

func (x *GetCampaignById) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *GetCampaignById) GetVtuber() *v11.VtuberProfile {
	if x != nil {
		return x.Vtuber
	}
	return nil
}

func (x *GetCampaignById) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *GetCampaignById) GetThumbnail() string {
	if x != nil {
		return x.Thumbnail
	}
	return ""
}

func (x *GetCampaignById) GetVariants() []*CampaignVariantById {
	if x != nil {
		return x.Variants
	}
	return nil
}

func (x *GetCampaignById) GetBanners() []*CampaignBanner {
	if x != nil {
		return x.Banners
	}
	return nil
}

func (x *GetCampaignById) GetHasLiked() bool {
	if x != nil {
		return x.HasLiked
	}
	return false
}

func (x *GetCampaignById) GetLikeCount() int64 {
	if x != nil {
		return x.LikeCount
	}
	return 0
}

func (x *GetCampaignById) GetPromotionalMessage() string {
	if x != nil {
		return x.PromotionalMessage
	}
	return ""
}

func (x *GetCampaignById) GetTotalRaised() int32 {
	if x != nil {
		return x.TotalRaised
	}
	return 0
}

func (x *GetCampaignById) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

func (x *GetCampaignById) GetSlug() string {
	if x != nil {
		return x.Slug
	}
	return ""
}

type CampaignVariantById struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CampaignId    string                 `protobuf:"bytes,2,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	Price         int32                  `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	MaxSub        int32                  `protobuf:"varint,5,opt,name=max_sub,json=maxSub,proto3" json:"max_sub,omitempty"`
	Image         string                 `protobuf:"bytes,6,opt,name=image,proto3" json:"image,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	SubCount      int64                  `protobuf:"varint,8,opt,name=sub_count,json=subCount,proto3" json:"sub_count,omitempty"`
	Title         string                 `protobuf:"bytes,9,opt,name=title,proto3" json:"title,omitempty"`
	CanSubscribe  bool                   `protobuf:"varint,10,opt,name=can_subscribe,json=canSubscribe,proto3" json:"can_subscribe,omitempty"`
	DisplayOrder  int32                  `protobuf:"varint,11,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CampaignVariantById) Reset() {
	*x = CampaignVariantById{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CampaignVariantById) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignVariantById) ProtoMessage() {}

func (x *CampaignVariantById) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignVariantById.ProtoReflect.Descriptor instead.
func (*CampaignVariantById) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{18}
}

func (x *CampaignVariantById) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CampaignVariantById) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *CampaignVariantById) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CampaignVariantById) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CampaignVariantById) GetMaxSub() int32 {
	if x != nil {
		return x.MaxSub
	}
	return 0
}

func (x *CampaignVariantById) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *CampaignVariantById) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CampaignVariantById) GetSubCount() int64 {
	if x != nil {
		return x.SubCount
	}
	return 0
}

func (x *CampaignVariantById) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CampaignVariantById) GetCanSubscribe() bool {
	if x != nil {
		return x.CanSubscribe
	}
	return false
}

func (x *CampaignVariantById) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

type GetCampaignByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *GetCampaignById       `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignByIdResponse) Reset() {
	*x = GetCampaignByIdResponse{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignByIdResponse) ProtoMessage() {}

func (x *GetCampaignByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignByIdResponse.ProtoReflect.Descriptor instead.
func (*GetCampaignByIdResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{19}
}

func (x *GetCampaignByIdResponse) GetData() *GetCampaignById {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteCampaignByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCampaignByIdRequest) Reset() {
	*x = DeleteCampaignByIdRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCampaignByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCampaignByIdRequest) ProtoMessage() {}

func (x *DeleteCampaignByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCampaignByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteCampaignByIdRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{20}
}

func (x *DeleteCampaignByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateCampaignByIdRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Name               string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" validate:"required"`
	Description        string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	StartDate          *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate            *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Id                 string                 `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	TotalBudget        int32                  `protobuf:"varint,6,opt,name=total_budget,json=totalBudget,proto3" json:"total_budget,omitempty" validate:"required"`
	Categories         []string               `protobuf:"bytes,7,rep,name=categories,proto3" json:"categories,omitempty"`
	Thumbnail          string                 `protobuf:"bytes,8,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
	ShortDescription   string                 `protobuf:"bytes,9,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty" validate:"required"`
	PromotionalMessage string                 `protobuf:"bytes,10,opt,name=promotional_message,json=promotionalMessage,proto3" json:"promotional_message,omitempty" validate:"required"`
	SocialMediaLinks   *v1.SocialMediaLinks   `protobuf:"bytes,11,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *UpdateCampaignByIdRequest) Reset() {
	*x = UpdateCampaignByIdRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCampaignByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCampaignByIdRequest) ProtoMessage() {}

func (x *UpdateCampaignByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCampaignByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdateCampaignByIdRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateCampaignByIdRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateCampaignByIdRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateCampaignByIdRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *UpdateCampaignByIdRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *UpdateCampaignByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateCampaignByIdRequest) GetTotalBudget() int32 {
	if x != nil {
		return x.TotalBudget
	}
	return 0
}

func (x *UpdateCampaignByIdRequest) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *UpdateCampaignByIdRequest) GetThumbnail() string {
	if x != nil {
		return x.Thumbnail
	}
	return ""
}

func (x *UpdateCampaignByIdRequest) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *UpdateCampaignByIdRequest) GetPromotionalMessage() string {
	if x != nil {
		return x.PromotionalMessage
	}
	return ""
}

func (x *UpdateCampaignByIdRequest) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

type CampaignSubscriptionComments struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	User          *v1.Profile            `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Comment       string                 `protobuf:"bytes,4,opt,name=comment,proto3" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CampaignSubscriptionComments) Reset() {
	*x = CampaignSubscriptionComments{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CampaignSubscriptionComments) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignSubscriptionComments) ProtoMessage() {}

func (x *CampaignSubscriptionComments) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignSubscriptionComments.ProtoReflect.Descriptor instead.
func (*CampaignSubscriptionComments) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{22}
}

func (x *CampaignSubscriptionComments) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CampaignSubscriptionComments) GetUser() *v1.Profile {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *CampaignSubscriptionComments) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CampaignSubscriptionComments) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

type GetCampaignSubscriptionCommentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CampaignId    string                 `protobuf:"bytes,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignSubscriptionCommentsRequest) Reset() {
	*x = GetCampaignSubscriptionCommentsRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignSubscriptionCommentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignSubscriptionCommentsRequest) ProtoMessage() {}

func (x *GetCampaignSubscriptionCommentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignSubscriptionCommentsRequest.ProtoReflect.Descriptor instead.
func (*GetCampaignSubscriptionCommentsRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{23}
}

func (x *GetCampaignSubscriptionCommentsRequest) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *GetCampaignSubscriptionCommentsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetCampaignSubscriptionCommentsReponse struct {
	state             protoimpl.MessageState          `protogen:"open.v1"`
	Data              []*CampaignSubscriptionComments `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails           `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetCampaignSubscriptionCommentsReponse) Reset() {
	*x = GetCampaignSubscriptionCommentsReponse{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignSubscriptionCommentsReponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignSubscriptionCommentsReponse) ProtoMessage() {}

func (x *GetCampaignSubscriptionCommentsReponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignSubscriptionCommentsReponse.ProtoReflect.Descriptor instead.
func (*GetCampaignSubscriptionCommentsReponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{24}
}

func (x *GetCampaignSubscriptionCommentsReponse) GetData() []*CampaignSubscriptionComments {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetCampaignSubscriptionCommentsReponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetSupportedCampaignRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSupportedCampaignRequest) Reset() {
	*x = GetSupportedCampaignRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSupportedCampaignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportedCampaignRequest) ProtoMessage() {}

func (x *GetSupportedCampaignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportedCampaignRequest.ProtoReflect.Descriptor instead.
func (*GetSupportedCampaignRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{25}
}

func (x *GetSupportedCampaignRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type PopularCampaignRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PopularCampaignRequest) Reset() {
	*x = PopularCampaignRequest{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PopularCampaignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PopularCampaignRequest) ProtoMessage() {}

func (x *PopularCampaignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PopularCampaignRequest.ProtoReflect.Descriptor instead.
func (*PopularCampaignRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{26}
}

type DeleteCampaignByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCampaignByIdResponse) Reset() {
	*x = DeleteCampaignByIdResponse{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCampaignByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCampaignByIdResponse) ProtoMessage() {}

func (x *DeleteCampaignByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCampaignByIdResponse.ProtoReflect.Descriptor instead.
func (*DeleteCampaignByIdResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{27}
}

func (x *DeleteCampaignByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DeleteCampaignByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type UpdateCampaignByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCampaignByIdResponse) Reset() {
	*x = UpdateCampaignByIdResponse{}
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCampaignByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCampaignByIdResponse) ProtoMessage() {}

func (x *UpdateCampaignByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaigns_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCampaignByIdResponse.ProtoReflect.Descriptor instead.
func (*UpdateCampaignByIdResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaigns_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateCampaignByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UpdateCampaignByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_campaigns_v1_campaigns_proto protoreflect.FileDescriptor

const file_campaigns_v1_campaigns_proto_rawDesc = "" +
	"\n" +
	"\x1ccampaigns/v1/campaigns.proto\x12\x10api.campaigns.v1\x1a\x14authz/v1/authz.proto\x1a!campaigns/v1/campaignbanner.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\x1a\x1fvtubers/v1/vtuberprofiles.proto\"\x83\x03\n" +
	"\fSubscription\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05image\x18\x04 \x01(\tR\x05image\x12\x1d\n" +
	"\acomment\x18\x05 \x01(\tH\x00R\acomment\x88\x01\x01\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1f\n" +
	"\vcampaign_id\x18\a \x01(\tR\n" +
	"campaignId\x12\x16\n" +
	"\x06amount\x18\b \x01(\x05R\x06amount\x12.\n" +
	"\x13campaign_variant_id\x18\t \x01(\tR\x11campaignVariantId\x124\n" +
	"\x16campaign_variant_title\x18\n" +
	" \x01(\tR\x14campaignVariantTitle\x124\n" +
	"\x16campaign_variant_image\x18\v \x01(\tR\x14campaignVariantImageB\n" +
	"\n" +
	"\b_comment\"\x99\x01\n" +
	"\x12SubscriberResponse\x122\n" +
	"\x04data\x18\x01 \x03(\v2\x1e.api.campaigns.v1.SubscriptionR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\x8a\x01\n" +
	"\x11SubscriberRequest\x12\x1f\n" +
	"\vcampaign_id\x18\x01 \x01(\tR\n" +
	"campaignId\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\x83\x01\n" +
	"\x15SearchCampaignRequest\x12\x14\n" +
	"\x05query\x18\x01 \x01(\tR\x05query\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\x1b\n" +
	"\x19GetMyCampaignsNameRequest\"3\n" +
	"\rCampaignNames\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\"Q\n" +
	"\x1aGetMyCampaignNamesResponse\x123\n" +
	"\x04data\x18\x01 \x03(\v2\x1f.api.campaigns.v1.CampaignNamesR\x04data\"\xca\x03\n" +
	"\x12AddCampaignRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12!\n" +
	"\ftotal_budget\x18\x05 \x01(\x05R\vtotalBudget\x12\x1e\n" +
	"\n" +
	"categories\x18\x06 \x03(\tR\n" +
	"categories\x12\x1c\n" +
	"\tthumbnail\x18\a \x01(\tR\tthumbnail\x12+\n" +
	"\x11short_description\x18\n" +
	" \x01(\tR\x10shortDescription\x12/\n" +
	"\x13promotional_message\x18\v \x01(\tR\x12promotionalMessage\x12M\n" +
	"\x12social_media_links\x18\f \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinks\"E\n" +
	"\x13AddCampaignResponse\x12.\n" +
	"\x04data\x18\x01 \x01(\v2\x1a.api.campaigns.v1.CampaignR\x04data\"\xf0\x04\n" +
	"\bCampaign\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12+\n" +
	"\x11short_description\x18\x03 \x01(\tR\x10shortDescription\x129\n" +
	"\n" +
	"start_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12!\n" +
	"\ftotal_budget\x18\x06 \x01(\x05R\vtotalBudget\x12\x1e\n" +
	"\n" +
	"categories\x18\a \x03(\tR\n" +
	"categories\x12\x1b\n" +
	"\tvtuber_id\x18\b \x01(\tR\bvtuberId\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1c\n" +
	"\tthumbnail\x18\n" +
	" \x01(\tR\tthumbnail\x12/\n" +
	"\x13promotional_message\x18\v \x01(\tR\x12promotionalMessage\x12!\n" +
	"\ftotal_raised\x18\f \x01(\x05R\vtotalRaised\x12M\n" +
	"\x12social_media_links\x18\r \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinks\x12\x12\n" +
	"\x04slug\x18\x0e \x01(\tR\x04slug\x12\"\n" +
	"\n" +
	"created_by\x18\x0f \x01(\tH\x00R\tcreatedBy\x88\x01\x01B\r\n" +
	"\v_created_by\"9\n" +
	"\x16RelatedCampaignRequest\x12\x1f\n" +
	"\vcampaign_id\x18\x01 \x01(\tR\n" +
	"campaignId\"I\n" +
	"\x17RelatedCampaignResponse\x12.\n" +
	"\x04data\x18\x01 \x03(\v2\x1a.api.campaigns.v1.CampaignR\x04data\"\xa4\x01\n" +
	"\x16GetAllCampaignsRequest\x12$\n" +
	"\vcategory_id\x18\x01 \x01(\tH\x00R\n" +
	"categoryId\x88\x01\x01\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x01R\n" +
	"pagination\x88\x01\x01B\x0e\n" +
	"\f_category_idB\r\n" +
	"\v_pagination\"\x93\x01\n" +
	"\x1eGetAllCampaignsByVtuberRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01\x12\x1b\n" +
	"\tvtuber_id\x18\x02 \x01(\tR\bvtuberIdB\r\n" +
	"\v_pagination\"m\n" +
	"\x15GetMyCampaignsRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\x9a\x01\n" +
	"\x17GetAllCampaignsResponse\x12.\n" +
	"\x04data\x18\x01 \x03(\v2\x1a.api.campaigns.v1.CampaignR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"(\n" +
	"\x16GetCampaignByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\xbb\x06\n" +
	"\x0fGetCampaignById\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12+\n" +
	"\x11short_description\x18\x04 \x01(\tR\x10shortDescription\x129\n" +
	"\n" +
	"start_date\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12!\n" +
	"\ftotal_budget\x18\a \x01(\x05R\vtotalBudget\x12\x1e\n" +
	"\n" +
	"categories\x18\b \x03(\tR\n" +
	"categories\x125\n" +
	"\x06vtuber\x18\t \x01(\v2\x1d.api.vtubers.v1.VtuberProfileR\x06vtuber\x129\n" +
	"\n" +
	"created_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1c\n" +
	"\tthumbnail\x18\v \x01(\tR\tthumbnail\x12A\n" +
	"\bvariants\x18\f \x03(\v2%.api.campaigns.v1.CampaignVariantByIdR\bvariants\x12:\n" +
	"\abanners\x18\r \x03(\v2 .api.campaigns.v1.CampaignBannerR\abanners\x12\x1b\n" +
	"\thas_liked\x18\x0e \x01(\bR\bhasLiked\x12\x1d\n" +
	"\n" +
	"like_count\x18\x0f \x01(\x03R\tlikeCount\x12/\n" +
	"\x13promotional_message\x18\x10 \x01(\tR\x12promotionalMessage\x12!\n" +
	"\ftotal_raised\x18\x11 \x01(\x05R\vtotalRaised\x12M\n" +
	"\x12social_media_links\x18\x12 \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinks\x12\x12\n" +
	"\x04slug\x18\x13 \x01(\tR\x04slug\"\xe5\x02\n" +
	"\x13CampaignVariantById\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1f\n" +
	"\vcampaign_id\x18\x02 \x01(\tR\n" +
	"campaignId\x12\x14\n" +
	"\x05price\x18\x03 \x01(\x05R\x05price\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x17\n" +
	"\amax_sub\x18\x05 \x01(\x05R\x06maxSub\x12\x14\n" +
	"\x05image\x18\x06 \x01(\tR\x05image\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1b\n" +
	"\tsub_count\x18\b \x01(\x03R\bsubCount\x12\x14\n" +
	"\x05title\x18\t \x01(\tR\x05title\x12#\n" +
	"\rcan_subscribe\x18\n" +
	" \x01(\bR\fcanSubscribe\x12#\n" +
	"\rdisplay_order\x18\v \x01(\x05R\fdisplayOrder\"P\n" +
	"\x17GetCampaignByIdResponse\x125\n" +
	"\x04data\x18\x01 \x01(\v2!.api.campaigns.v1.GetCampaignByIdR\x04data\"+\n" +
	"\x19DeleteCampaignByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\xe1\x03\n" +
	"\x19UpdateCampaignByIdRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12\x0e\n" +
	"\x02id\x18\x05 \x01(\tR\x02id\x12!\n" +
	"\ftotal_budget\x18\x06 \x01(\x05R\vtotalBudget\x12\x1e\n" +
	"\n" +
	"categories\x18\a \x03(\tR\n" +
	"categories\x12\x1c\n" +
	"\tthumbnail\x18\b \x01(\tR\tthumbnail\x12+\n" +
	"\x11short_description\x18\t \x01(\tR\x10shortDescription\x12/\n" +
	"\x13promotional_message\x18\n" +
	" \x01(\tR\x12promotionalMessage\x12M\n" +
	"\x12social_media_links\x18\v \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinks\"\xaf\x01\n" +
	"\x1cCampaignSubscriptionComments\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12*\n" +
	"\x04user\x18\x02 \x01(\v2\x16.api.shared.v1.ProfileR\x04user\x129\n" +
	"\n" +
	"created_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x18\n" +
	"\acomment\x18\x04 \x01(\tR\acomment\"\x9f\x01\n" +
	"&GetCampaignSubscriptionCommentsRequest\x12\x1f\n" +
	"\vcampaign_id\x18\x01 \x01(\tR\n" +
	"campaignId\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\xbd\x01\n" +
	"&GetCampaignSubscriptionCommentsReponse\x12B\n" +
	"\x04data\x18\x01 \x03(\v2..api.campaigns.v1.CampaignSubscriptionCommentsR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"s\n" +
	"\x1bGetSupportedCampaignRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\x18\n" +
	"\x16PopularCampaignRequest\"P\n" +
	"\x1aDeleteCampaignByIdResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\"P\n" +
	"\x1aUpdateCampaignByIdResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess2\x84\x0f\n" +
	"\x0fCampaignService\x12z\n" +
	"\vAddCampaign\x12$.api.campaigns.v1.AddCampaignRequest\x1a%.api.campaigns.v1.AddCampaignResponse\"\x1e\x82\xb5\x18\x1a\b\x01\x18\x01\"\x14_user.vtuberId!=null\x12[\n" +
	"\x0eGetSubscribers\x12#.api.campaigns.v1.SubscriberRequest\x1a$.api.campaigns.v1.SubscriberResponse\x12f\n" +
	"\x0fGetAllCampaigns\x12(.api.campaigns.v1.GetAllCampaignsRequest\x1a).api.campaigns.v1.GetAllCampaignsResponse\x12l\n" +
	"\x15GetAllActiveCampaigns\x12(.api.campaigns.v1.GetAllCampaignsRequest\x1a).api.campaigns.v1.GetAllCampaignsResponse\x12x\n" +
	"\x19GetAllCampaignsByVtuberId\x120.api.campaigns.v1.GetAllCampaignsByVtuberRequest\x1a).api.campaigns.v1.GetAllCampaignsResponse\x12~\n" +
	"\x1fGetAllActiveCampaignsByVtuberId\x120.api.campaigns.v1.GetAllCampaignsByVtuberRequest\x1a).api.campaigns.v1.GetAllCampaignsResponse\x12\x82\x01\n" +
	"\x0eGetMyCampaigns\x12'.api.campaigns.v1.GetMyCampaignsRequest\x1a).api.campaigns.v1.GetAllCampaignsResponse\"\x1c\x82\xb5\x18\x18\b\x01\"\x14_user.vtuberId!=null\x12h\n" +
	"\x0fGetCampaignById\x12(.api.campaigns.v1.GetCampaignByIdRequest\x1a).api.campaigns.v1.GetCampaignByIdResponse\"\x00\x12\x8d\x01\n" +
	"\x12DeleteCampaignById\x12+.api.campaigns.v1.DeleteCampaignByIdRequest\x1a,.api.campaigns.v1.DeleteCampaignByIdResponse\"\x1c\x82\xb5\x18\x18\b\x01\"\x14_user.vtuberId!=null\x12\x8d\x01\n" +
	"\x12UpdateCampaignById\x12+.api.campaigns.v1.UpdateCampaignByIdRequest\x1a,.api.campaigns.v1.UpdateCampaignByIdResponse\"\x1c\x82\xb5\x18\x18\b\x01\"\x14_user.vtuberId!=null\x12\x95\x01\n" +
	"\x1dGetCampaignSubscriberComments\x128.api.campaigns.v1.GetCampaignSubscriptionCommentsRequest\x1a8.api.campaigns.v1.GetCampaignSubscriptionCommentsReponse\"\x00\x12{\n" +
	"\x17GetMySupportedCampaigns\x12-.api.campaigns.v1.GetSupportedCampaignRequest\x1a).api.campaigns.v1.GetAllCampaignsResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\\\n" +
	"\x12GetPopularCampaign\x12(.api.campaigns.v1.PopularCampaignRequest\x1a\x1a.api.campaigns.v1.Campaign\"\x00\x12k\n" +
	"\x12GetRelatedCampaign\x12(.api.campaigns.v1.RelatedCampaignRequest\x1a).api.campaigns.v1.RelatedCampaignResponse\"\x00\x12q\n" +
	"\x12GetMyCampaignNames\x12+.api.campaigns.v1.GetMyCampaignsNameRequest\x1a,.api.campaigns.v1.GetMyCampaignNamesResponse\"\x00\x12f\n" +
	"\x0eSearchCampaign\x12'.api.campaigns.v1.SearchCampaignRequest\x1a).api.campaigns.v1.GetAllCampaignsResponse\"\x00B8Z6github.com/nsp-inc/vtuber/api/campaigns/v1;campaignsv1b\x06proto3"

var (
	file_campaigns_v1_campaigns_proto_rawDescOnce sync.Once
	file_campaigns_v1_campaigns_proto_rawDescData []byte
)

func file_campaigns_v1_campaigns_proto_rawDescGZIP() []byte {
	file_campaigns_v1_campaigns_proto_rawDescOnce.Do(func() {
		file_campaigns_v1_campaigns_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_campaigns_v1_campaigns_proto_rawDesc), len(file_campaigns_v1_campaigns_proto_rawDesc)))
	})
	return file_campaigns_v1_campaigns_proto_rawDescData
}

var file_campaigns_v1_campaigns_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_campaigns_v1_campaigns_proto_goTypes = []any{
	(*Subscription)(nil),                           // 0: api.campaigns.v1.Subscription
	(*SubscriberResponse)(nil),                     // 1: api.campaigns.v1.SubscriberResponse
	(*SubscriberRequest)(nil),                      // 2: api.campaigns.v1.SubscriberRequest
	(*SearchCampaignRequest)(nil),                  // 3: api.campaigns.v1.SearchCampaignRequest
	(*GetMyCampaignsNameRequest)(nil),              // 4: api.campaigns.v1.GetMyCampaignsNameRequest
	(*CampaignNames)(nil),                          // 5: api.campaigns.v1.CampaignNames
	(*GetMyCampaignNamesResponse)(nil),             // 6: api.campaigns.v1.GetMyCampaignNamesResponse
	(*AddCampaignRequest)(nil),                     // 7: api.campaigns.v1.AddCampaignRequest
	(*AddCampaignResponse)(nil),                    // 8: api.campaigns.v1.AddCampaignResponse
	(*Campaign)(nil),                               // 9: api.campaigns.v1.Campaign
	(*RelatedCampaignRequest)(nil),                 // 10: api.campaigns.v1.RelatedCampaignRequest
	(*RelatedCampaignResponse)(nil),                // 11: api.campaigns.v1.RelatedCampaignResponse
	(*GetAllCampaignsRequest)(nil),                 // 12: api.campaigns.v1.GetAllCampaignsRequest
	(*GetAllCampaignsByVtuberRequest)(nil),         // 13: api.campaigns.v1.GetAllCampaignsByVtuberRequest
	(*GetMyCampaignsRequest)(nil),                  // 14: api.campaigns.v1.GetMyCampaignsRequest
	(*GetAllCampaignsResponse)(nil),                // 15: api.campaigns.v1.GetAllCampaignsResponse
	(*GetCampaignByIdRequest)(nil),                 // 16: api.campaigns.v1.GetCampaignByIdRequest
	(*GetCampaignById)(nil),                        // 17: api.campaigns.v1.GetCampaignById
	(*CampaignVariantById)(nil),                    // 18: api.campaigns.v1.CampaignVariantById
	(*GetCampaignByIdResponse)(nil),                // 19: api.campaigns.v1.GetCampaignByIdResponse
	(*DeleteCampaignByIdRequest)(nil),              // 20: api.campaigns.v1.DeleteCampaignByIdRequest
	(*UpdateCampaignByIdRequest)(nil),              // 21: api.campaigns.v1.UpdateCampaignByIdRequest
	(*CampaignSubscriptionComments)(nil),           // 22: api.campaigns.v1.CampaignSubscriptionComments
	(*GetCampaignSubscriptionCommentsRequest)(nil), // 23: api.campaigns.v1.GetCampaignSubscriptionCommentsRequest
	(*GetCampaignSubscriptionCommentsReponse)(nil), // 24: api.campaigns.v1.GetCampaignSubscriptionCommentsReponse
	(*GetSupportedCampaignRequest)(nil),            // 25: api.campaigns.v1.GetSupportedCampaignRequest
	(*PopularCampaignRequest)(nil),                 // 26: api.campaigns.v1.PopularCampaignRequest
	(*DeleteCampaignByIdResponse)(nil),             // 27: api.campaigns.v1.DeleteCampaignByIdResponse
	(*UpdateCampaignByIdResponse)(nil),             // 28: api.campaigns.v1.UpdateCampaignByIdResponse
	(*timestamppb.Timestamp)(nil),                  // 29: google.protobuf.Timestamp
	(*v1.PaginationDetails)(nil),                   // 30: api.shared.v1.PaginationDetails
	(*v1.PaginationRequest)(nil),                   // 31: api.shared.v1.PaginationRequest
	(*v1.SocialMediaLinks)(nil),                    // 32: api.shared.v1.SocialMediaLinks
	(*v11.VtuberProfile)(nil),                      // 33: api.vtubers.v1.VtuberProfile
	(*CampaignBanner)(nil),                         // 34: api.campaigns.v1.CampaignBanner
	(*v1.Profile)(nil),                             // 35: api.shared.v1.Profile
}
var file_campaigns_v1_campaigns_proto_depIdxs = []int32{
	29, // 0: api.campaigns.v1.Subscription.created_at:type_name -> google.protobuf.Timestamp
	0,  // 1: api.campaigns.v1.SubscriberResponse.data:type_name -> api.campaigns.v1.Subscription
	30, // 2: api.campaigns.v1.SubscriberResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	31, // 3: api.campaigns.v1.SubscriberRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	31, // 4: api.campaigns.v1.SearchCampaignRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	5,  // 5: api.campaigns.v1.GetMyCampaignNamesResponse.data:type_name -> api.campaigns.v1.CampaignNames
	29, // 6: api.campaigns.v1.AddCampaignRequest.start_date:type_name -> google.protobuf.Timestamp
	29, // 7: api.campaigns.v1.AddCampaignRequest.end_date:type_name -> google.protobuf.Timestamp
	32, // 8: api.campaigns.v1.AddCampaignRequest.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	9,  // 9: api.campaigns.v1.AddCampaignResponse.data:type_name -> api.campaigns.v1.Campaign
	29, // 10: api.campaigns.v1.Campaign.start_date:type_name -> google.protobuf.Timestamp
	29, // 11: api.campaigns.v1.Campaign.end_date:type_name -> google.protobuf.Timestamp
	29, // 12: api.campaigns.v1.Campaign.created_at:type_name -> google.protobuf.Timestamp
	32, // 13: api.campaigns.v1.Campaign.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	9,  // 14: api.campaigns.v1.RelatedCampaignResponse.data:type_name -> api.campaigns.v1.Campaign
	31, // 15: api.campaigns.v1.GetAllCampaignsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	31, // 16: api.campaigns.v1.GetAllCampaignsByVtuberRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	31, // 17: api.campaigns.v1.GetMyCampaignsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	9,  // 18: api.campaigns.v1.GetAllCampaignsResponse.data:type_name -> api.campaigns.v1.Campaign
	30, // 19: api.campaigns.v1.GetAllCampaignsResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	29, // 20: api.campaigns.v1.GetCampaignById.start_date:type_name -> google.protobuf.Timestamp
	29, // 21: api.campaigns.v1.GetCampaignById.end_date:type_name -> google.protobuf.Timestamp
	33, // 22: api.campaigns.v1.GetCampaignById.vtuber:type_name -> api.vtubers.v1.VtuberProfile
	29, // 23: api.campaigns.v1.GetCampaignById.created_at:type_name -> google.protobuf.Timestamp
	18, // 24: api.campaigns.v1.GetCampaignById.variants:type_name -> api.campaigns.v1.CampaignVariantById
	34, // 25: api.campaigns.v1.GetCampaignById.banners:type_name -> api.campaigns.v1.CampaignBanner
	32, // 26: api.campaigns.v1.GetCampaignById.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	29, // 27: api.campaigns.v1.CampaignVariantById.created_at:type_name -> google.protobuf.Timestamp
	17, // 28: api.campaigns.v1.GetCampaignByIdResponse.data:type_name -> api.campaigns.v1.GetCampaignById
	29, // 29: api.campaigns.v1.UpdateCampaignByIdRequest.start_date:type_name -> google.protobuf.Timestamp
	29, // 30: api.campaigns.v1.UpdateCampaignByIdRequest.end_date:type_name -> google.protobuf.Timestamp
	32, // 31: api.campaigns.v1.UpdateCampaignByIdRequest.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	35, // 32: api.campaigns.v1.CampaignSubscriptionComments.user:type_name -> api.shared.v1.Profile
	29, // 33: api.campaigns.v1.CampaignSubscriptionComments.created_at:type_name -> google.protobuf.Timestamp
	31, // 34: api.campaigns.v1.GetCampaignSubscriptionCommentsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	22, // 35: api.campaigns.v1.GetCampaignSubscriptionCommentsReponse.data:type_name -> api.campaigns.v1.CampaignSubscriptionComments
	30, // 36: api.campaigns.v1.GetCampaignSubscriptionCommentsReponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	31, // 37: api.campaigns.v1.GetSupportedCampaignRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	7,  // 38: api.campaigns.v1.CampaignService.AddCampaign:input_type -> api.campaigns.v1.AddCampaignRequest
	2,  // 39: api.campaigns.v1.CampaignService.GetSubscribers:input_type -> api.campaigns.v1.SubscriberRequest
	12, // 40: api.campaigns.v1.CampaignService.GetAllCampaigns:input_type -> api.campaigns.v1.GetAllCampaignsRequest
	12, // 41: api.campaigns.v1.CampaignService.GetAllActiveCampaigns:input_type -> api.campaigns.v1.GetAllCampaignsRequest
	13, // 42: api.campaigns.v1.CampaignService.GetAllCampaignsByVtuberId:input_type -> api.campaigns.v1.GetAllCampaignsByVtuberRequest
	13, // 43: api.campaigns.v1.CampaignService.GetAllActiveCampaignsByVtuberId:input_type -> api.campaigns.v1.GetAllCampaignsByVtuberRequest
	14, // 44: api.campaigns.v1.CampaignService.GetMyCampaigns:input_type -> api.campaigns.v1.GetMyCampaignsRequest
	16, // 45: api.campaigns.v1.CampaignService.GetCampaignById:input_type -> api.campaigns.v1.GetCampaignByIdRequest
	20, // 46: api.campaigns.v1.CampaignService.DeleteCampaignById:input_type -> api.campaigns.v1.DeleteCampaignByIdRequest
	21, // 47: api.campaigns.v1.CampaignService.UpdateCampaignById:input_type -> api.campaigns.v1.UpdateCampaignByIdRequest
	23, // 48: api.campaigns.v1.CampaignService.GetCampaignSubscriberComments:input_type -> api.campaigns.v1.GetCampaignSubscriptionCommentsRequest
	25, // 49: api.campaigns.v1.CampaignService.GetMySupportedCampaigns:input_type -> api.campaigns.v1.GetSupportedCampaignRequest
	26, // 50: api.campaigns.v1.CampaignService.GetPopularCampaign:input_type -> api.campaigns.v1.PopularCampaignRequest
	10, // 51: api.campaigns.v1.CampaignService.GetRelatedCampaign:input_type -> api.campaigns.v1.RelatedCampaignRequest
	4,  // 52: api.campaigns.v1.CampaignService.GetMyCampaignNames:input_type -> api.campaigns.v1.GetMyCampaignsNameRequest
	3,  // 53: api.campaigns.v1.CampaignService.SearchCampaign:input_type -> api.campaigns.v1.SearchCampaignRequest
	8,  // 54: api.campaigns.v1.CampaignService.AddCampaign:output_type -> api.campaigns.v1.AddCampaignResponse
	1,  // 55: api.campaigns.v1.CampaignService.GetSubscribers:output_type -> api.campaigns.v1.SubscriberResponse
	15, // 56: api.campaigns.v1.CampaignService.GetAllCampaigns:output_type -> api.campaigns.v1.GetAllCampaignsResponse
	15, // 57: api.campaigns.v1.CampaignService.GetAllActiveCampaigns:output_type -> api.campaigns.v1.GetAllCampaignsResponse
	15, // 58: api.campaigns.v1.CampaignService.GetAllCampaignsByVtuberId:output_type -> api.campaigns.v1.GetAllCampaignsResponse
	15, // 59: api.campaigns.v1.CampaignService.GetAllActiveCampaignsByVtuberId:output_type -> api.campaigns.v1.GetAllCampaignsResponse
	15, // 60: api.campaigns.v1.CampaignService.GetMyCampaigns:output_type -> api.campaigns.v1.GetAllCampaignsResponse
	19, // 61: api.campaigns.v1.CampaignService.GetCampaignById:output_type -> api.campaigns.v1.GetCampaignByIdResponse
	27, // 62: api.campaigns.v1.CampaignService.DeleteCampaignById:output_type -> api.campaigns.v1.DeleteCampaignByIdResponse
	28, // 63: api.campaigns.v1.CampaignService.UpdateCampaignById:output_type -> api.campaigns.v1.UpdateCampaignByIdResponse
	24, // 64: api.campaigns.v1.CampaignService.GetCampaignSubscriberComments:output_type -> api.campaigns.v1.GetCampaignSubscriptionCommentsReponse
	15, // 65: api.campaigns.v1.CampaignService.GetMySupportedCampaigns:output_type -> api.campaigns.v1.GetAllCampaignsResponse
	9,  // 66: api.campaigns.v1.CampaignService.GetPopularCampaign:output_type -> api.campaigns.v1.Campaign
	11, // 67: api.campaigns.v1.CampaignService.GetRelatedCampaign:output_type -> api.campaigns.v1.RelatedCampaignResponse
	6,  // 68: api.campaigns.v1.CampaignService.GetMyCampaignNames:output_type -> api.campaigns.v1.GetMyCampaignNamesResponse
	15, // 69: api.campaigns.v1.CampaignService.SearchCampaign:output_type -> api.campaigns.v1.GetAllCampaignsResponse
	54, // [54:70] is the sub-list for method output_type
	38, // [38:54] is the sub-list for method input_type
	38, // [38:38] is the sub-list for extension type_name
	38, // [38:38] is the sub-list for extension extendee
	0,  // [0:38] is the sub-list for field type_name
}

func init() { file_campaigns_v1_campaigns_proto_init() }
func file_campaigns_v1_campaigns_proto_init() {
	if File_campaigns_v1_campaigns_proto != nil {
		return
	}
	file_campaigns_v1_campaignbanner_proto_init()
	file_campaigns_v1_campaigns_proto_msgTypes[0].OneofWrappers = []any{}
	file_campaigns_v1_campaigns_proto_msgTypes[2].OneofWrappers = []any{}
	file_campaigns_v1_campaigns_proto_msgTypes[3].OneofWrappers = []any{}
	file_campaigns_v1_campaigns_proto_msgTypes[9].OneofWrappers = []any{}
	file_campaigns_v1_campaigns_proto_msgTypes[12].OneofWrappers = []any{}
	file_campaigns_v1_campaigns_proto_msgTypes[13].OneofWrappers = []any{}
	file_campaigns_v1_campaigns_proto_msgTypes[14].OneofWrappers = []any{}
	file_campaigns_v1_campaigns_proto_msgTypes[23].OneofWrappers = []any{}
	file_campaigns_v1_campaigns_proto_msgTypes[25].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_campaigns_v1_campaigns_proto_rawDesc), len(file_campaigns_v1_campaigns_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_campaigns_v1_campaigns_proto_goTypes,
		DependencyIndexes: file_campaigns_v1_campaigns_proto_depIdxs,
		MessageInfos:      file_campaigns_v1_campaigns_proto_msgTypes,
	}.Build()
	File_campaigns_v1_campaigns_proto = out.File
	file_campaigns_v1_campaigns_proto_goTypes = nil
	file_campaigns_v1_campaigns_proto_depIdxs = nil
}
