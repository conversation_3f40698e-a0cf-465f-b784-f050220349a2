// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: campaigns/v1/campaignvariant.proto

package campaignsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddCampaignVariantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CampaignId    string                 `protobuf:"bytes,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty" validate:"required"`
	Price         int32                  `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty" validate:"gte=1"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	MaxSub        int32                  `protobuf:"varint,4,opt,name=max_sub,json=maxSub,proto3" json:"max_sub,omitempty" validate:"gte=0"`
	Image         string                 `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty" validate:"required"`
	Title         string                 `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty" validate:"required"`
	DisplayOrder  int32                  `protobuf:"varint,7,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty" validate:"gte=0"`
	MaxSubForUser int32                  `protobuf:"varint,8,opt,name=max_sub_for_user,json=maxSubForUser,proto3" json:"max_sub_for_user,omitempty" validate:"gte=0"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCampaignVariantRequest) Reset() {
	*x = AddCampaignVariantRequest{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCampaignVariantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCampaignVariantRequest) ProtoMessage() {}

func (x *AddCampaignVariantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCampaignVariantRequest.ProtoReflect.Descriptor instead.
func (*AddCampaignVariantRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{0}
}

func (x *AddCampaignVariantRequest) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *AddCampaignVariantRequest) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *AddCampaignVariantRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AddCampaignVariantRequest) GetMaxSub() int32 {
	if x != nil {
		return x.MaxSub
	}
	return 0
}

func (x *AddCampaignVariantRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *AddCampaignVariantRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AddCampaignVariantRequest) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

func (x *AddCampaignVariantRequest) GetMaxSubForUser() int32 {
	if x != nil {
		return x.MaxSubForUser
	}
	return 0
}

type GetCampaignSubResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Subs          []*SubDetails          `protobuf:"bytes,1,rep,name=subs,proto3" json:"subs,omitempty"`
	Pagination    *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignSubResponse) Reset() {
	*x = GetCampaignSubResponse{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignSubResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignSubResponse) ProtoMessage() {}

func (x *GetCampaignSubResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignSubResponse.ProtoReflect.Descriptor instead.
func (*GetCampaignSubResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{1}
}

func (x *GetCampaignSubResponse) GetSubs() []*SubDetails {
	if x != nil {
		return x.Subs
	}
	return nil
}

func (x *GetCampaignSubResponse) GetPagination() *v1.PaginationDetails {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type SubDetails struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Image         *string                `protobuf:"bytes,3,opt,name=image,proto3,oneof" json:"image,omitempty"`
	Amount        int32                  `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubDetails) Reset() {
	*x = SubDetails{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubDetails) ProtoMessage() {}

func (x *SubDetails) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubDetails.ProtoReflect.Descriptor instead.
func (*SubDetails) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{2}
}

func (x *SubDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SubDetails) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SubDetails) GetImage() string {
	if x != nil && x.Image != nil {
		return *x.Image
	}
	return ""
}

func (x *SubDetails) GetAmount() int32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type GetCampaignSubRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignSubRequest) Reset() {
	*x = GetCampaignSubRequest{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignSubRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignSubRequest) ProtoMessage() {}

func (x *GetCampaignSubRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignSubRequest.ProtoReflect.Descriptor instead.
func (*GetCampaignSubRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{3}
}

func (x *GetCampaignSubRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetCampaignSubRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type AddCampaignVariantResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *CampaignVariant       `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCampaignVariantResponse) Reset() {
	*x = AddCampaignVariantResponse{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCampaignVariantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCampaignVariantResponse) ProtoMessage() {}

func (x *AddCampaignVariantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCampaignVariantResponse.ProtoReflect.Descriptor instead.
func (*AddCampaignVariantResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{4}
}

func (x *AddCampaignVariantResponse) GetData() *CampaignVariant {
	if x != nil {
		return x.Data
	}
	return nil
}

type CampaignVariant struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CampaignId    string                 `protobuf:"bytes,2,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	Price         int32                  `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	MaxSub        int32                  `protobuf:"varint,5,opt,name=max_sub,json=maxSub,proto3" json:"max_sub,omitempty"`
	Image         string                 `protobuf:"bytes,6,opt,name=image,proto3" json:"image,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	SubCount      int64                  `protobuf:"varint,8,opt,name=sub_count,json=subCount,proto3" json:"sub_count,omitempty"`
	Title         string                 `protobuf:"bytes,9,opt,name=title,proto3" json:"title,omitempty"`
	DisplayOrder  int32                  `protobuf:"varint,10,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty"`
	MaxSubForUser int32                  `protobuf:"varint,11,opt,name=max_sub_for_user,json=maxSubForUser,proto3" json:"max_sub_for_user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CampaignVariant) Reset() {
	*x = CampaignVariant{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CampaignVariant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignVariant) ProtoMessage() {}

func (x *CampaignVariant) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignVariant.ProtoReflect.Descriptor instead.
func (*CampaignVariant) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{5}
}

func (x *CampaignVariant) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CampaignVariant) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *CampaignVariant) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CampaignVariant) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CampaignVariant) GetMaxSub() int32 {
	if x != nil {
		return x.MaxSub
	}
	return 0
}

func (x *CampaignVariant) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *CampaignVariant) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CampaignVariant) GetSubCount() int64 {
	if x != nil {
		return x.SubCount
	}
	return 0
}

func (x *CampaignVariant) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CampaignVariant) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

func (x *CampaignVariant) GetMaxSubForUser() int32 {
	if x != nil {
		return x.MaxSubForUser
	}
	return 0
}

type GetAllCampaignVariantsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CampaignId    string                 `protobuf:"bytes,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllCampaignVariantsRequest) Reset() {
	*x = GetAllCampaignVariantsRequest{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllCampaignVariantsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllCampaignVariantsRequest) ProtoMessage() {}

func (x *GetAllCampaignVariantsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllCampaignVariantsRequest.ProtoReflect.Descriptor instead.
func (*GetAllCampaignVariantsRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{6}
}

func (x *GetAllCampaignVariantsRequest) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

type GetAllCampaignVariantsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*CampaignVariant     `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllCampaignVariantsResponse) Reset() {
	*x = GetAllCampaignVariantsResponse{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllCampaignVariantsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllCampaignVariantsResponse) ProtoMessage() {}

func (x *GetAllCampaignVariantsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllCampaignVariantsResponse.ProtoReflect.Descriptor instead.
func (*GetAllCampaignVariantsResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{7}
}

func (x *GetAllCampaignVariantsResponse) GetData() []*CampaignVariant {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetCampaignVariantByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignVariantByIdRequest) Reset() {
	*x = GetCampaignVariantByIdRequest{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignVariantByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignVariantByIdRequest) ProtoMessage() {}

func (x *GetCampaignVariantByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignVariantByIdRequest.ProtoReflect.Descriptor instead.
func (*GetCampaignVariantByIdRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{8}
}

func (x *GetCampaignVariantByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetCampaignVariantByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *CampaignVariant       `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignVariantByIdResponse) Reset() {
	*x = GetCampaignVariantByIdResponse{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignVariantByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignVariantByIdResponse) ProtoMessage() {}

func (x *GetCampaignVariantByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignVariantByIdResponse.ProtoReflect.Descriptor instead.
func (*GetCampaignVariantByIdResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{9}
}

func (x *GetCampaignVariantByIdResponse) GetData() *CampaignVariant {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteCampaignVariantByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCampaignVariantByIdRequest) Reset() {
	*x = DeleteCampaignVariantByIdRequest{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCampaignVariantByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCampaignVariantByIdRequest) ProtoMessage() {}

func (x *DeleteCampaignVariantByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCampaignVariantByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteCampaignVariantByIdRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteCampaignVariantByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateCampaignVariantByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Price         int32                  `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty" validate:"required"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	MaxSub        int32                  `protobuf:"varint,5,opt,name=max_sub,json=maxSub,proto3" json:"max_sub,omitempty" validate:"gte=0"`
	Image         string                 `protobuf:"bytes,6,opt,name=image,proto3" json:"image,omitempty" validate:"required"`
	Title         string                 `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty" validate:"required"`
	DisplayOrder  int32                  `protobuf:"varint,8,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty" validate:"gte=0"`
	MaxSubForUser int32                  `protobuf:"varint,9,opt,name=max_sub_for_user,json=maxSubForUser,proto3" json:"max_sub_for_user,omitempty" validate:"gte=0"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCampaignVariantByIdRequest) Reset() {
	*x = UpdateCampaignVariantByIdRequest{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCampaignVariantByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCampaignVariantByIdRequest) ProtoMessage() {}

func (x *UpdateCampaignVariantByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCampaignVariantByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdateCampaignVariantByIdRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateCampaignVariantByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateCampaignVariantByIdRequest) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *UpdateCampaignVariantByIdRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateCampaignVariantByIdRequest) GetMaxSub() int32 {
	if x != nil {
		return x.MaxSub
	}
	return 0
}

func (x *UpdateCampaignVariantByIdRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UpdateCampaignVariantByIdRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateCampaignVariantByIdRequest) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

func (x *UpdateCampaignVariantByIdRequest) GetMaxSubForUser() int32 {
	if x != nil {
		return x.MaxSubForUser
	}
	return 0
}

type GetCampaignVariantSubsByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignVariantSubsByIdRequest) Reset() {
	*x = GetCampaignVariantSubsByIdRequest{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignVariantSubsByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignVariantSubsByIdRequest) ProtoMessage() {}

func (x *GetCampaignVariantSubsByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignVariantSubsByIdRequest.ProtoReflect.Descriptor instead.
func (*GetCampaignVariantSubsByIdRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{12}
}

func (x *GetCampaignVariantSubsByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteCampaignVariantByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCampaignVariantByIdResponse) Reset() {
	*x = DeleteCampaignVariantByIdResponse{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCampaignVariantByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCampaignVariantByIdResponse) ProtoMessage() {}

func (x *DeleteCampaignVariantByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCampaignVariantByIdResponse.ProtoReflect.Descriptor instead.
func (*DeleteCampaignVariantByIdResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteCampaignVariantByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteCampaignVariantByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateCampaignVariantByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCampaignVariantByIdResponse) Reset() {
	*x = UpdateCampaignVariantByIdResponse{}
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCampaignVariantByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCampaignVariantByIdResponse) ProtoMessage() {}

func (x *UpdateCampaignVariantByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignvariant_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCampaignVariantByIdResponse.ProtoReflect.Descriptor instead.
func (*UpdateCampaignVariantByIdResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignvariant_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateCampaignVariantByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateCampaignVariantByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_campaigns_v1_campaignvariant_proto protoreflect.FileDescriptor

const file_campaigns_v1_campaignvariant_proto_rawDesc = "" +
	"\n" +
	"\"campaigns/v1/campaignvariant.proto\x12\x10api.campaigns.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\"\x87\x02\n" +
	"\x19AddCampaignVariantRequest\x12\x1f\n" +
	"\vcampaign_id\x18\x01 \x01(\tR\n" +
	"campaignId\x12\x14\n" +
	"\x05price\x18\x02 \x01(\x05R\x05price\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x17\n" +
	"\amax_sub\x18\x04 \x01(\x05R\x06maxSub\x12\x14\n" +
	"\x05image\x18\x05 \x01(\tR\x05image\x12\x14\n" +
	"\x05title\x18\x06 \x01(\tR\x05title\x12#\n" +
	"\rdisplay_order\x18\a \x01(\x05R\fdisplayOrder\x12'\n" +
	"\x10max_sub_for_user\x18\b \x01(\x05R\rmaxSubForUser\"\xa0\x01\n" +
	"\x16GetCampaignSubResponse\x120\n" +
	"\x04subs\x18\x01 \x03(\v2\x1c.api.campaigns.v1.SubDetailsR\x04subs\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"m\n" +
	"\n" +
	"SubDetails\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x19\n" +
	"\x05image\x18\x03 \x01(\tH\x00R\x05image\x88\x01\x01\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\x05R\x06amountB\b\n" +
	"\x06_image\"}\n" +
	"\x15GetCampaignSubRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"S\n" +
	"\x1aAddCampaignVariantResponse\x125\n" +
	"\x04data\x18\x01 \x01(\v2!.api.campaigns.v1.CampaignVariantR\x04data\"\xe5\x02\n" +
	"\x0fCampaignVariant\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1f\n" +
	"\vcampaign_id\x18\x02 \x01(\tR\n" +
	"campaignId\x12\x14\n" +
	"\x05price\x18\x03 \x01(\x05R\x05price\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x17\n" +
	"\amax_sub\x18\x05 \x01(\x05R\x06maxSub\x12\x14\n" +
	"\x05image\x18\x06 \x01(\tR\x05image\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1b\n" +
	"\tsub_count\x18\b \x01(\x03R\bsubCount\x12\x14\n" +
	"\x05title\x18\t \x01(\tR\x05title\x12#\n" +
	"\rdisplay_order\x18\n" +
	" \x01(\x05R\fdisplayOrder\x12'\n" +
	"\x10max_sub_for_user\x18\v \x01(\x05R\rmaxSubForUser\"@\n" +
	"\x1dGetAllCampaignVariantsRequest\x12\x1f\n" +
	"\vcampaign_id\x18\x01 \x01(\tR\n" +
	"campaignId\"W\n" +
	"\x1eGetAllCampaignVariantsResponse\x125\n" +
	"\x04data\x18\x01 \x03(\v2!.api.campaigns.v1.CampaignVariantR\x04data\"/\n" +
	"\x1dGetCampaignVariantByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"W\n" +
	"\x1eGetCampaignVariantByIdResponse\x125\n" +
	"\x04data\x18\x01 \x01(\v2!.api.campaigns.v1.CampaignVariantR\x04data\"2\n" +
	" DeleteCampaignVariantByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\xfd\x01\n" +
	" UpdateCampaignVariantByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05price\x18\x03 \x01(\x05R\x05price\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x17\n" +
	"\amax_sub\x18\x05 \x01(\x05R\x06maxSub\x12\x14\n" +
	"\x05image\x18\x06 \x01(\tR\x05image\x12\x14\n" +
	"\x05title\x18\a \x01(\tR\x05title\x12#\n" +
	"\rdisplay_order\x18\b \x01(\x05R\fdisplayOrder\x12'\n" +
	"\x10max_sub_for_user\x18\t \x01(\x05R\rmaxSubForUser\"3\n" +
	"!GetCampaignVariantSubsByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"W\n" +
	"!DeleteCampaignVariantByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"W\n" +
	"!UpdateCampaignVariantByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\xeb\x06\n" +
	"\x16CampaignVariantService\x12\x8f\x01\n" +
	"\x12AddCampaignVariant\x12+.api.campaigns.v1.AddCampaignVariantRequest\x1a,.api.campaigns.v1.AddCampaignVariantResponse\"\x1e\x82\xb5\x18\x1a\b\x01\x18\x01\"\x14_user.vtuberId!=null\x12}\n" +
	"\x16GetAllCampaignVariants\x12/.api.campaigns.v1.GetAllCampaignVariantsRequest\x1a0.api.campaigns.v1.GetAllCampaignVariantsResponse\"\x00\x12}\n" +
	"\x16GetCampaignVariantById\x12/.api.campaigns.v1.GetCampaignVariantByIdRequest\x1a0.api.campaigns.v1.GetCampaignVariantByIdResponse\"\x00\x12\xa4\x01\n" +
	"\x19DeleteCampaignVariantById\x122.api.campaigns.v1.DeleteCampaignVariantByIdRequest\x1a3.api.campaigns.v1.DeleteCampaignVariantByIdResponse\"\x1e\x82\xb5\x18\x1a\b\x01\x18\x01\"\x14_user.vtuberId!=null\x12\xa4\x01\n" +
	"\x19UpdateCampaignVariantById\x122.api.campaigns.v1.UpdateCampaignVariantByIdRequest\x1a3.api.campaigns.v1.UpdateCampaignVariantByIdResponse\"\x1e\x82\xb5\x18\x1a\b\x01\x18\x01\"\x14_user.vtuberId!=null\x12s\n" +
	"\x16GetCampaignVariantSubs\x12'.api.campaigns.v1.GetCampaignSubRequest\x1a(.api.campaigns.v1.GetCampaignSubResponse\"\x06\x82\xb5\x18\x02\b\x01B8Z6github.com/nsp-inc/vtuber/api/campaigns/v1;campaignsv1b\x06proto3"

var (
	file_campaigns_v1_campaignvariant_proto_rawDescOnce sync.Once
	file_campaigns_v1_campaignvariant_proto_rawDescData []byte
)

func file_campaigns_v1_campaignvariant_proto_rawDescGZIP() []byte {
	file_campaigns_v1_campaignvariant_proto_rawDescOnce.Do(func() {
		file_campaigns_v1_campaignvariant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_campaigns_v1_campaignvariant_proto_rawDesc), len(file_campaigns_v1_campaignvariant_proto_rawDesc)))
	})
	return file_campaigns_v1_campaignvariant_proto_rawDescData
}

var file_campaigns_v1_campaignvariant_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_campaigns_v1_campaignvariant_proto_goTypes = []any{
	(*AddCampaignVariantRequest)(nil),         // 0: api.campaigns.v1.AddCampaignVariantRequest
	(*GetCampaignSubResponse)(nil),            // 1: api.campaigns.v1.GetCampaignSubResponse
	(*SubDetails)(nil),                        // 2: api.campaigns.v1.SubDetails
	(*GetCampaignSubRequest)(nil),             // 3: api.campaigns.v1.GetCampaignSubRequest
	(*AddCampaignVariantResponse)(nil),        // 4: api.campaigns.v1.AddCampaignVariantResponse
	(*CampaignVariant)(nil),                   // 5: api.campaigns.v1.CampaignVariant
	(*GetAllCampaignVariantsRequest)(nil),     // 6: api.campaigns.v1.GetAllCampaignVariantsRequest
	(*GetAllCampaignVariantsResponse)(nil),    // 7: api.campaigns.v1.GetAllCampaignVariantsResponse
	(*GetCampaignVariantByIdRequest)(nil),     // 8: api.campaigns.v1.GetCampaignVariantByIdRequest
	(*GetCampaignVariantByIdResponse)(nil),    // 9: api.campaigns.v1.GetCampaignVariantByIdResponse
	(*DeleteCampaignVariantByIdRequest)(nil),  // 10: api.campaigns.v1.DeleteCampaignVariantByIdRequest
	(*UpdateCampaignVariantByIdRequest)(nil),  // 11: api.campaigns.v1.UpdateCampaignVariantByIdRequest
	(*GetCampaignVariantSubsByIdRequest)(nil), // 12: api.campaigns.v1.GetCampaignVariantSubsByIdRequest
	(*DeleteCampaignVariantByIdResponse)(nil), // 13: api.campaigns.v1.DeleteCampaignVariantByIdResponse
	(*UpdateCampaignVariantByIdResponse)(nil), // 14: api.campaigns.v1.UpdateCampaignVariantByIdResponse
	(*v1.PaginationDetails)(nil),              // 15: api.shared.v1.PaginationDetails
	(*v1.PaginationRequest)(nil),              // 16: api.shared.v1.PaginationRequest
	(*timestamppb.Timestamp)(nil),             // 17: google.protobuf.Timestamp
}
var file_campaigns_v1_campaignvariant_proto_depIdxs = []int32{
	2,  // 0: api.campaigns.v1.GetCampaignSubResponse.subs:type_name -> api.campaigns.v1.SubDetails
	15, // 1: api.campaigns.v1.GetCampaignSubResponse.pagination:type_name -> api.shared.v1.PaginationDetails
	16, // 2: api.campaigns.v1.GetCampaignSubRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	5,  // 3: api.campaigns.v1.AddCampaignVariantResponse.data:type_name -> api.campaigns.v1.CampaignVariant
	17, // 4: api.campaigns.v1.CampaignVariant.created_at:type_name -> google.protobuf.Timestamp
	5,  // 5: api.campaigns.v1.GetAllCampaignVariantsResponse.data:type_name -> api.campaigns.v1.CampaignVariant
	5,  // 6: api.campaigns.v1.GetCampaignVariantByIdResponse.data:type_name -> api.campaigns.v1.CampaignVariant
	0,  // 7: api.campaigns.v1.CampaignVariantService.AddCampaignVariant:input_type -> api.campaigns.v1.AddCampaignVariantRequest
	6,  // 8: api.campaigns.v1.CampaignVariantService.GetAllCampaignVariants:input_type -> api.campaigns.v1.GetAllCampaignVariantsRequest
	8,  // 9: api.campaigns.v1.CampaignVariantService.GetCampaignVariantById:input_type -> api.campaigns.v1.GetCampaignVariantByIdRequest
	10, // 10: api.campaigns.v1.CampaignVariantService.DeleteCampaignVariantById:input_type -> api.campaigns.v1.DeleteCampaignVariantByIdRequest
	11, // 11: api.campaigns.v1.CampaignVariantService.UpdateCampaignVariantById:input_type -> api.campaigns.v1.UpdateCampaignVariantByIdRequest
	3,  // 12: api.campaigns.v1.CampaignVariantService.GetCampaignVariantSubs:input_type -> api.campaigns.v1.GetCampaignSubRequest
	4,  // 13: api.campaigns.v1.CampaignVariantService.AddCampaignVariant:output_type -> api.campaigns.v1.AddCampaignVariantResponse
	7,  // 14: api.campaigns.v1.CampaignVariantService.GetAllCampaignVariants:output_type -> api.campaigns.v1.GetAllCampaignVariantsResponse
	9,  // 15: api.campaigns.v1.CampaignVariantService.GetCampaignVariantById:output_type -> api.campaigns.v1.GetCampaignVariantByIdResponse
	13, // 16: api.campaigns.v1.CampaignVariantService.DeleteCampaignVariantById:output_type -> api.campaigns.v1.DeleteCampaignVariantByIdResponse
	14, // 17: api.campaigns.v1.CampaignVariantService.UpdateCampaignVariantById:output_type -> api.campaigns.v1.UpdateCampaignVariantByIdResponse
	1,  // 18: api.campaigns.v1.CampaignVariantService.GetCampaignVariantSubs:output_type -> api.campaigns.v1.GetCampaignSubResponse
	13, // [13:19] is the sub-list for method output_type
	7,  // [7:13] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_campaigns_v1_campaignvariant_proto_init() }
func file_campaigns_v1_campaignvariant_proto_init() {
	if File_campaigns_v1_campaignvariant_proto != nil {
		return
	}
	file_campaigns_v1_campaignvariant_proto_msgTypes[1].OneofWrappers = []any{}
	file_campaigns_v1_campaignvariant_proto_msgTypes[2].OneofWrappers = []any{}
	file_campaigns_v1_campaignvariant_proto_msgTypes[3].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_campaigns_v1_campaignvariant_proto_rawDesc), len(file_campaigns_v1_campaignvariant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_campaigns_v1_campaignvariant_proto_goTypes,
		DependencyIndexes: file_campaigns_v1_campaignvariant_proto_depIdxs,
		MessageInfos:      file_campaigns_v1_campaignvariant_proto_msgTypes,
	}.Build()
	File_campaigns_v1_campaignvariant_proto = out.File
	file_campaigns_v1_campaignvariant_proto_goTypes = nil
	file_campaigns_v1_campaignvariant_proto_depIdxs = nil
}
