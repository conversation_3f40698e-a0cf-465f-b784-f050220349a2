// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: campaigns/v1/campaigns.proto

package campaignsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/campaigns/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CampaignServiceName is the fully-qualified name of the CampaignService service.
	CampaignServiceName = "api.campaigns.v1.CampaignService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CampaignServiceAddCampaignProcedure is the fully-qualified name of the CampaignService's
	// AddCampaign RPC.
	CampaignServiceAddCampaignProcedure = "/api.campaigns.v1.CampaignService/AddCampaign"
	// CampaignServiceGetSubscribersProcedure is the fully-qualified name of the CampaignService's
	// GetSubscribers RPC.
	CampaignServiceGetSubscribersProcedure = "/api.campaigns.v1.CampaignService/GetSubscribers"
	// CampaignServiceGetAllCampaignsProcedure is the fully-qualified name of the CampaignService's
	// GetAllCampaigns RPC.
	CampaignServiceGetAllCampaignsProcedure = "/api.campaigns.v1.CampaignService/GetAllCampaigns"
	// CampaignServiceGetAllActiveCampaignsProcedure is the fully-qualified name of the
	// CampaignService's GetAllActiveCampaigns RPC.
	CampaignServiceGetAllActiveCampaignsProcedure = "/api.campaigns.v1.CampaignService/GetAllActiveCampaigns"
	// CampaignServiceGetAllCampaignsByVtuberIdProcedure is the fully-qualified name of the
	// CampaignService's GetAllCampaignsByVtuberId RPC.
	CampaignServiceGetAllCampaignsByVtuberIdProcedure = "/api.campaigns.v1.CampaignService/GetAllCampaignsByVtuberId"
	// CampaignServiceGetAllActiveCampaignsByVtuberIdProcedure is the fully-qualified name of the
	// CampaignService's GetAllActiveCampaignsByVtuberId RPC.
	CampaignServiceGetAllActiveCampaignsByVtuberIdProcedure = "/api.campaigns.v1.CampaignService/GetAllActiveCampaignsByVtuberId"
	// CampaignServiceGetMyCampaignsProcedure is the fully-qualified name of the CampaignService's
	// GetMyCampaigns RPC.
	CampaignServiceGetMyCampaignsProcedure = "/api.campaigns.v1.CampaignService/GetMyCampaigns"
	// CampaignServiceGetCampaignByIdProcedure is the fully-qualified name of the CampaignService's
	// GetCampaignById RPC.
	CampaignServiceGetCampaignByIdProcedure = "/api.campaigns.v1.CampaignService/GetCampaignById"
	// CampaignServiceDeleteCampaignByIdProcedure is the fully-qualified name of the CampaignService's
	// DeleteCampaignById RPC.
	CampaignServiceDeleteCampaignByIdProcedure = "/api.campaigns.v1.CampaignService/DeleteCampaignById"
	// CampaignServiceUpdateCampaignByIdProcedure is the fully-qualified name of the CampaignService's
	// UpdateCampaignById RPC.
	CampaignServiceUpdateCampaignByIdProcedure = "/api.campaigns.v1.CampaignService/UpdateCampaignById"
	// CampaignServiceGetCampaignSubscriberCommentsProcedure is the fully-qualified name of the
	// CampaignService's GetCampaignSubscriberComments RPC.
	CampaignServiceGetCampaignSubscriberCommentsProcedure = "/api.campaigns.v1.CampaignService/GetCampaignSubscriberComments"
	// CampaignServiceGetMySupportedCampaignsProcedure is the fully-qualified name of the
	// CampaignService's GetMySupportedCampaigns RPC.
	CampaignServiceGetMySupportedCampaignsProcedure = "/api.campaigns.v1.CampaignService/GetMySupportedCampaigns"
	// CampaignServiceGetPopularCampaignProcedure is the fully-qualified name of the CampaignService's
	// GetPopularCampaign RPC.
	CampaignServiceGetPopularCampaignProcedure = "/api.campaigns.v1.CampaignService/GetPopularCampaign"
	// CampaignServiceGetRelatedCampaignProcedure is the fully-qualified name of the CampaignService's
	// GetRelatedCampaign RPC.
	CampaignServiceGetRelatedCampaignProcedure = "/api.campaigns.v1.CampaignService/GetRelatedCampaign"
	// CampaignServiceGetMyCampaignNamesProcedure is the fully-qualified name of the CampaignService's
	// GetMyCampaignNames RPC.
	CampaignServiceGetMyCampaignNamesProcedure = "/api.campaigns.v1.CampaignService/GetMyCampaignNames"
	// CampaignServiceSearchCampaignProcedure is the fully-qualified name of the CampaignService's
	// SearchCampaign RPC.
	CampaignServiceSearchCampaignProcedure = "/api.campaigns.v1.CampaignService/SearchCampaign"
)

// CampaignServiceClient is a client for the api.campaigns.v1.CampaignService service.
type CampaignServiceClient interface {
	AddCampaign(context.Context, *connect.Request[v1.AddCampaignRequest]) (*connect.Response[v1.AddCampaignResponse], error)
	GetSubscribers(context.Context, *connect.Request[v1.SubscriberRequest]) (*connect.Response[v1.SubscriberResponse], error)
	GetAllCampaigns(context.Context, *connect.Request[v1.GetAllCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetAllActiveCampaigns(context.Context, *connect.Request[v1.GetAllCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetAllCampaignsByVtuberId(context.Context, *connect.Request[v1.GetAllCampaignsByVtuberRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetAllActiveCampaignsByVtuberId(context.Context, *connect.Request[v1.GetAllCampaignsByVtuberRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetMyCampaigns(context.Context, *connect.Request[v1.GetMyCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetCampaignById(context.Context, *connect.Request[v1.GetCampaignByIdRequest]) (*connect.Response[v1.GetCampaignByIdResponse], error)
	DeleteCampaignById(context.Context, *connect.Request[v1.DeleteCampaignByIdRequest]) (*connect.Response[v1.DeleteCampaignByIdResponse], error)
	UpdateCampaignById(context.Context, *connect.Request[v1.UpdateCampaignByIdRequest]) (*connect.Response[v1.UpdateCampaignByIdResponse], error)
	GetCampaignSubscriberComments(context.Context, *connect.Request[v1.GetCampaignSubscriptionCommentsRequest]) (*connect.Response[v1.GetCampaignSubscriptionCommentsReponse], error)
	GetMySupportedCampaigns(context.Context, *connect.Request[v1.GetSupportedCampaignRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetPopularCampaign(context.Context, *connect.Request[v1.PopularCampaignRequest]) (*connect.Response[v1.Campaign], error)
	GetRelatedCampaign(context.Context, *connect.Request[v1.RelatedCampaignRequest]) (*connect.Response[v1.RelatedCampaignResponse], error)
	GetMyCampaignNames(context.Context, *connect.Request[v1.GetMyCampaignsNameRequest]) (*connect.Response[v1.GetMyCampaignNamesResponse], error)
	SearchCampaign(context.Context, *connect.Request[v1.SearchCampaignRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
}

// NewCampaignServiceClient constructs a client for the api.campaigns.v1.CampaignService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCampaignServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CampaignServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	campaignServiceMethods := v1.File_campaigns_v1_campaigns_proto.Services().ByName("CampaignService").Methods()
	return &campaignServiceClient{
		addCampaign: connect.NewClient[v1.AddCampaignRequest, v1.AddCampaignResponse](
			httpClient,
			baseURL+CampaignServiceAddCampaignProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("AddCampaign")),
			connect.WithClientOptions(opts...),
		),
		getSubscribers: connect.NewClient[v1.SubscriberRequest, v1.SubscriberResponse](
			httpClient,
			baseURL+CampaignServiceGetSubscribersProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetSubscribers")),
			connect.WithClientOptions(opts...),
		),
		getAllCampaigns: connect.NewClient[v1.GetAllCampaignsRequest, v1.GetAllCampaignsResponse](
			httpClient,
			baseURL+CampaignServiceGetAllCampaignsProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetAllCampaigns")),
			connect.WithClientOptions(opts...),
		),
		getAllActiveCampaigns: connect.NewClient[v1.GetAllCampaignsRequest, v1.GetAllCampaignsResponse](
			httpClient,
			baseURL+CampaignServiceGetAllActiveCampaignsProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetAllActiveCampaigns")),
			connect.WithClientOptions(opts...),
		),
		getAllCampaignsByVtuberId: connect.NewClient[v1.GetAllCampaignsByVtuberRequest, v1.GetAllCampaignsResponse](
			httpClient,
			baseURL+CampaignServiceGetAllCampaignsByVtuberIdProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetAllCampaignsByVtuberId")),
			connect.WithClientOptions(opts...),
		),
		getAllActiveCampaignsByVtuberId: connect.NewClient[v1.GetAllCampaignsByVtuberRequest, v1.GetAllCampaignsResponse](
			httpClient,
			baseURL+CampaignServiceGetAllActiveCampaignsByVtuberIdProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetAllActiveCampaignsByVtuberId")),
			connect.WithClientOptions(opts...),
		),
		getMyCampaigns: connect.NewClient[v1.GetMyCampaignsRequest, v1.GetAllCampaignsResponse](
			httpClient,
			baseURL+CampaignServiceGetMyCampaignsProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetMyCampaigns")),
			connect.WithClientOptions(opts...),
		),
		getCampaignById: connect.NewClient[v1.GetCampaignByIdRequest, v1.GetCampaignByIdResponse](
			httpClient,
			baseURL+CampaignServiceGetCampaignByIdProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetCampaignById")),
			connect.WithClientOptions(opts...),
		),
		deleteCampaignById: connect.NewClient[v1.DeleteCampaignByIdRequest, v1.DeleteCampaignByIdResponse](
			httpClient,
			baseURL+CampaignServiceDeleteCampaignByIdProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("DeleteCampaignById")),
			connect.WithClientOptions(opts...),
		),
		updateCampaignById: connect.NewClient[v1.UpdateCampaignByIdRequest, v1.UpdateCampaignByIdResponse](
			httpClient,
			baseURL+CampaignServiceUpdateCampaignByIdProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("UpdateCampaignById")),
			connect.WithClientOptions(opts...),
		),
		getCampaignSubscriberComments: connect.NewClient[v1.GetCampaignSubscriptionCommentsRequest, v1.GetCampaignSubscriptionCommentsReponse](
			httpClient,
			baseURL+CampaignServiceGetCampaignSubscriberCommentsProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetCampaignSubscriberComments")),
			connect.WithClientOptions(opts...),
		),
		getMySupportedCampaigns: connect.NewClient[v1.GetSupportedCampaignRequest, v1.GetAllCampaignsResponse](
			httpClient,
			baseURL+CampaignServiceGetMySupportedCampaignsProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetMySupportedCampaigns")),
			connect.WithClientOptions(opts...),
		),
		getPopularCampaign: connect.NewClient[v1.PopularCampaignRequest, v1.Campaign](
			httpClient,
			baseURL+CampaignServiceGetPopularCampaignProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetPopularCampaign")),
			connect.WithClientOptions(opts...),
		),
		getRelatedCampaign: connect.NewClient[v1.RelatedCampaignRequest, v1.RelatedCampaignResponse](
			httpClient,
			baseURL+CampaignServiceGetRelatedCampaignProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetRelatedCampaign")),
			connect.WithClientOptions(opts...),
		),
		getMyCampaignNames: connect.NewClient[v1.GetMyCampaignsNameRequest, v1.GetMyCampaignNamesResponse](
			httpClient,
			baseURL+CampaignServiceGetMyCampaignNamesProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("GetMyCampaignNames")),
			connect.WithClientOptions(opts...),
		),
		searchCampaign: connect.NewClient[v1.SearchCampaignRequest, v1.GetAllCampaignsResponse](
			httpClient,
			baseURL+CampaignServiceSearchCampaignProcedure,
			connect.WithSchema(campaignServiceMethods.ByName("SearchCampaign")),
			connect.WithClientOptions(opts...),
		),
	}
}

// campaignServiceClient implements CampaignServiceClient.
type campaignServiceClient struct {
	addCampaign                     *connect.Client[v1.AddCampaignRequest, v1.AddCampaignResponse]
	getSubscribers                  *connect.Client[v1.SubscriberRequest, v1.SubscriberResponse]
	getAllCampaigns                 *connect.Client[v1.GetAllCampaignsRequest, v1.GetAllCampaignsResponse]
	getAllActiveCampaigns           *connect.Client[v1.GetAllCampaignsRequest, v1.GetAllCampaignsResponse]
	getAllCampaignsByVtuberId       *connect.Client[v1.GetAllCampaignsByVtuberRequest, v1.GetAllCampaignsResponse]
	getAllActiveCampaignsByVtuberId *connect.Client[v1.GetAllCampaignsByVtuberRequest, v1.GetAllCampaignsResponse]
	getMyCampaigns                  *connect.Client[v1.GetMyCampaignsRequest, v1.GetAllCampaignsResponse]
	getCampaignById                 *connect.Client[v1.GetCampaignByIdRequest, v1.GetCampaignByIdResponse]
	deleteCampaignById              *connect.Client[v1.DeleteCampaignByIdRequest, v1.DeleteCampaignByIdResponse]
	updateCampaignById              *connect.Client[v1.UpdateCampaignByIdRequest, v1.UpdateCampaignByIdResponse]
	getCampaignSubscriberComments   *connect.Client[v1.GetCampaignSubscriptionCommentsRequest, v1.GetCampaignSubscriptionCommentsReponse]
	getMySupportedCampaigns         *connect.Client[v1.GetSupportedCampaignRequest, v1.GetAllCampaignsResponse]
	getPopularCampaign              *connect.Client[v1.PopularCampaignRequest, v1.Campaign]
	getRelatedCampaign              *connect.Client[v1.RelatedCampaignRequest, v1.RelatedCampaignResponse]
	getMyCampaignNames              *connect.Client[v1.GetMyCampaignsNameRequest, v1.GetMyCampaignNamesResponse]
	searchCampaign                  *connect.Client[v1.SearchCampaignRequest, v1.GetAllCampaignsResponse]
}

// AddCampaign calls api.campaigns.v1.CampaignService.AddCampaign.
func (c *campaignServiceClient) AddCampaign(ctx context.Context, req *connect.Request[v1.AddCampaignRequest]) (*connect.Response[v1.AddCampaignResponse], error) {
	return c.addCampaign.CallUnary(ctx, req)
}

// GetSubscribers calls api.campaigns.v1.CampaignService.GetSubscribers.
func (c *campaignServiceClient) GetSubscribers(ctx context.Context, req *connect.Request[v1.SubscriberRequest]) (*connect.Response[v1.SubscriberResponse], error) {
	return c.getSubscribers.CallUnary(ctx, req)
}

// GetAllCampaigns calls api.campaigns.v1.CampaignService.GetAllCampaigns.
func (c *campaignServiceClient) GetAllCampaigns(ctx context.Context, req *connect.Request[v1.GetAllCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return c.getAllCampaigns.CallUnary(ctx, req)
}

// GetAllActiveCampaigns calls api.campaigns.v1.CampaignService.GetAllActiveCampaigns.
func (c *campaignServiceClient) GetAllActiveCampaigns(ctx context.Context, req *connect.Request[v1.GetAllCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return c.getAllActiveCampaigns.CallUnary(ctx, req)
}

// GetAllCampaignsByVtuberId calls api.campaigns.v1.CampaignService.GetAllCampaignsByVtuberId.
func (c *campaignServiceClient) GetAllCampaignsByVtuberId(ctx context.Context, req *connect.Request[v1.GetAllCampaignsByVtuberRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return c.getAllCampaignsByVtuberId.CallUnary(ctx, req)
}

// GetAllActiveCampaignsByVtuberId calls
// api.campaigns.v1.CampaignService.GetAllActiveCampaignsByVtuberId.
func (c *campaignServiceClient) GetAllActiveCampaignsByVtuberId(ctx context.Context, req *connect.Request[v1.GetAllCampaignsByVtuberRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return c.getAllActiveCampaignsByVtuberId.CallUnary(ctx, req)
}

// GetMyCampaigns calls api.campaigns.v1.CampaignService.GetMyCampaigns.
func (c *campaignServiceClient) GetMyCampaigns(ctx context.Context, req *connect.Request[v1.GetMyCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return c.getMyCampaigns.CallUnary(ctx, req)
}

// GetCampaignById calls api.campaigns.v1.CampaignService.GetCampaignById.
func (c *campaignServiceClient) GetCampaignById(ctx context.Context, req *connect.Request[v1.GetCampaignByIdRequest]) (*connect.Response[v1.GetCampaignByIdResponse], error) {
	return c.getCampaignById.CallUnary(ctx, req)
}

// DeleteCampaignById calls api.campaigns.v1.CampaignService.DeleteCampaignById.
func (c *campaignServiceClient) DeleteCampaignById(ctx context.Context, req *connect.Request[v1.DeleteCampaignByIdRequest]) (*connect.Response[v1.DeleteCampaignByIdResponse], error) {
	return c.deleteCampaignById.CallUnary(ctx, req)
}

// UpdateCampaignById calls api.campaigns.v1.CampaignService.UpdateCampaignById.
func (c *campaignServiceClient) UpdateCampaignById(ctx context.Context, req *connect.Request[v1.UpdateCampaignByIdRequest]) (*connect.Response[v1.UpdateCampaignByIdResponse], error) {
	return c.updateCampaignById.CallUnary(ctx, req)
}

// GetCampaignSubscriberComments calls
// api.campaigns.v1.CampaignService.GetCampaignSubscriberComments.
func (c *campaignServiceClient) GetCampaignSubscriberComments(ctx context.Context, req *connect.Request[v1.GetCampaignSubscriptionCommentsRequest]) (*connect.Response[v1.GetCampaignSubscriptionCommentsReponse], error) {
	return c.getCampaignSubscriberComments.CallUnary(ctx, req)
}

// GetMySupportedCampaigns calls api.campaigns.v1.CampaignService.GetMySupportedCampaigns.
func (c *campaignServiceClient) GetMySupportedCampaigns(ctx context.Context, req *connect.Request[v1.GetSupportedCampaignRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return c.getMySupportedCampaigns.CallUnary(ctx, req)
}

// GetPopularCampaign calls api.campaigns.v1.CampaignService.GetPopularCampaign.
func (c *campaignServiceClient) GetPopularCampaign(ctx context.Context, req *connect.Request[v1.PopularCampaignRequest]) (*connect.Response[v1.Campaign], error) {
	return c.getPopularCampaign.CallUnary(ctx, req)
}

// GetRelatedCampaign calls api.campaigns.v1.CampaignService.GetRelatedCampaign.
func (c *campaignServiceClient) GetRelatedCampaign(ctx context.Context, req *connect.Request[v1.RelatedCampaignRequest]) (*connect.Response[v1.RelatedCampaignResponse], error) {
	return c.getRelatedCampaign.CallUnary(ctx, req)
}

// GetMyCampaignNames calls api.campaigns.v1.CampaignService.GetMyCampaignNames.
func (c *campaignServiceClient) GetMyCampaignNames(ctx context.Context, req *connect.Request[v1.GetMyCampaignsNameRequest]) (*connect.Response[v1.GetMyCampaignNamesResponse], error) {
	return c.getMyCampaignNames.CallUnary(ctx, req)
}

// SearchCampaign calls api.campaigns.v1.CampaignService.SearchCampaign.
func (c *campaignServiceClient) SearchCampaign(ctx context.Context, req *connect.Request[v1.SearchCampaignRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return c.searchCampaign.CallUnary(ctx, req)
}

// CampaignServiceHandler is an implementation of the api.campaigns.v1.CampaignService service.
type CampaignServiceHandler interface {
	AddCampaign(context.Context, *connect.Request[v1.AddCampaignRequest]) (*connect.Response[v1.AddCampaignResponse], error)
	GetSubscribers(context.Context, *connect.Request[v1.SubscriberRequest]) (*connect.Response[v1.SubscriberResponse], error)
	GetAllCampaigns(context.Context, *connect.Request[v1.GetAllCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetAllActiveCampaigns(context.Context, *connect.Request[v1.GetAllCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetAllCampaignsByVtuberId(context.Context, *connect.Request[v1.GetAllCampaignsByVtuberRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetAllActiveCampaignsByVtuberId(context.Context, *connect.Request[v1.GetAllCampaignsByVtuberRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetMyCampaigns(context.Context, *connect.Request[v1.GetMyCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetCampaignById(context.Context, *connect.Request[v1.GetCampaignByIdRequest]) (*connect.Response[v1.GetCampaignByIdResponse], error)
	DeleteCampaignById(context.Context, *connect.Request[v1.DeleteCampaignByIdRequest]) (*connect.Response[v1.DeleteCampaignByIdResponse], error)
	UpdateCampaignById(context.Context, *connect.Request[v1.UpdateCampaignByIdRequest]) (*connect.Response[v1.UpdateCampaignByIdResponse], error)
	GetCampaignSubscriberComments(context.Context, *connect.Request[v1.GetCampaignSubscriptionCommentsRequest]) (*connect.Response[v1.GetCampaignSubscriptionCommentsReponse], error)
	GetMySupportedCampaigns(context.Context, *connect.Request[v1.GetSupportedCampaignRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
	GetPopularCampaign(context.Context, *connect.Request[v1.PopularCampaignRequest]) (*connect.Response[v1.Campaign], error)
	GetRelatedCampaign(context.Context, *connect.Request[v1.RelatedCampaignRequest]) (*connect.Response[v1.RelatedCampaignResponse], error)
	GetMyCampaignNames(context.Context, *connect.Request[v1.GetMyCampaignsNameRequest]) (*connect.Response[v1.GetMyCampaignNamesResponse], error)
	SearchCampaign(context.Context, *connect.Request[v1.SearchCampaignRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error)
}

// NewCampaignServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCampaignServiceHandler(svc CampaignServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	campaignServiceMethods := v1.File_campaigns_v1_campaigns_proto.Services().ByName("CampaignService").Methods()
	campaignServiceAddCampaignHandler := connect.NewUnaryHandler(
		CampaignServiceAddCampaignProcedure,
		svc.AddCampaign,
		connect.WithSchema(campaignServiceMethods.ByName("AddCampaign")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetSubscribersHandler := connect.NewUnaryHandler(
		CampaignServiceGetSubscribersProcedure,
		svc.GetSubscribers,
		connect.WithSchema(campaignServiceMethods.ByName("GetSubscribers")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetAllCampaignsHandler := connect.NewUnaryHandler(
		CampaignServiceGetAllCampaignsProcedure,
		svc.GetAllCampaigns,
		connect.WithSchema(campaignServiceMethods.ByName("GetAllCampaigns")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetAllActiveCampaignsHandler := connect.NewUnaryHandler(
		CampaignServiceGetAllActiveCampaignsProcedure,
		svc.GetAllActiveCampaigns,
		connect.WithSchema(campaignServiceMethods.ByName("GetAllActiveCampaigns")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetAllCampaignsByVtuberIdHandler := connect.NewUnaryHandler(
		CampaignServiceGetAllCampaignsByVtuberIdProcedure,
		svc.GetAllCampaignsByVtuberId,
		connect.WithSchema(campaignServiceMethods.ByName("GetAllCampaignsByVtuberId")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetAllActiveCampaignsByVtuberIdHandler := connect.NewUnaryHandler(
		CampaignServiceGetAllActiveCampaignsByVtuberIdProcedure,
		svc.GetAllActiveCampaignsByVtuberId,
		connect.WithSchema(campaignServiceMethods.ByName("GetAllActiveCampaignsByVtuberId")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetMyCampaignsHandler := connect.NewUnaryHandler(
		CampaignServiceGetMyCampaignsProcedure,
		svc.GetMyCampaigns,
		connect.WithSchema(campaignServiceMethods.ByName("GetMyCampaigns")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetCampaignByIdHandler := connect.NewUnaryHandler(
		CampaignServiceGetCampaignByIdProcedure,
		svc.GetCampaignById,
		connect.WithSchema(campaignServiceMethods.ByName("GetCampaignById")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceDeleteCampaignByIdHandler := connect.NewUnaryHandler(
		CampaignServiceDeleteCampaignByIdProcedure,
		svc.DeleteCampaignById,
		connect.WithSchema(campaignServiceMethods.ByName("DeleteCampaignById")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceUpdateCampaignByIdHandler := connect.NewUnaryHandler(
		CampaignServiceUpdateCampaignByIdProcedure,
		svc.UpdateCampaignById,
		connect.WithSchema(campaignServiceMethods.ByName("UpdateCampaignById")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetCampaignSubscriberCommentsHandler := connect.NewUnaryHandler(
		CampaignServiceGetCampaignSubscriberCommentsProcedure,
		svc.GetCampaignSubscriberComments,
		connect.WithSchema(campaignServiceMethods.ByName("GetCampaignSubscriberComments")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetMySupportedCampaignsHandler := connect.NewUnaryHandler(
		CampaignServiceGetMySupportedCampaignsProcedure,
		svc.GetMySupportedCampaigns,
		connect.WithSchema(campaignServiceMethods.ByName("GetMySupportedCampaigns")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetPopularCampaignHandler := connect.NewUnaryHandler(
		CampaignServiceGetPopularCampaignProcedure,
		svc.GetPopularCampaign,
		connect.WithSchema(campaignServiceMethods.ByName("GetPopularCampaign")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetRelatedCampaignHandler := connect.NewUnaryHandler(
		CampaignServiceGetRelatedCampaignProcedure,
		svc.GetRelatedCampaign,
		connect.WithSchema(campaignServiceMethods.ByName("GetRelatedCampaign")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceGetMyCampaignNamesHandler := connect.NewUnaryHandler(
		CampaignServiceGetMyCampaignNamesProcedure,
		svc.GetMyCampaignNames,
		connect.WithSchema(campaignServiceMethods.ByName("GetMyCampaignNames")),
		connect.WithHandlerOptions(opts...),
	)
	campaignServiceSearchCampaignHandler := connect.NewUnaryHandler(
		CampaignServiceSearchCampaignProcedure,
		svc.SearchCampaign,
		connect.WithSchema(campaignServiceMethods.ByName("SearchCampaign")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.campaigns.v1.CampaignService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CampaignServiceAddCampaignProcedure:
			campaignServiceAddCampaignHandler.ServeHTTP(w, r)
		case CampaignServiceGetSubscribersProcedure:
			campaignServiceGetSubscribersHandler.ServeHTTP(w, r)
		case CampaignServiceGetAllCampaignsProcedure:
			campaignServiceGetAllCampaignsHandler.ServeHTTP(w, r)
		case CampaignServiceGetAllActiveCampaignsProcedure:
			campaignServiceGetAllActiveCampaignsHandler.ServeHTTP(w, r)
		case CampaignServiceGetAllCampaignsByVtuberIdProcedure:
			campaignServiceGetAllCampaignsByVtuberIdHandler.ServeHTTP(w, r)
		case CampaignServiceGetAllActiveCampaignsByVtuberIdProcedure:
			campaignServiceGetAllActiveCampaignsByVtuberIdHandler.ServeHTTP(w, r)
		case CampaignServiceGetMyCampaignsProcedure:
			campaignServiceGetMyCampaignsHandler.ServeHTTP(w, r)
		case CampaignServiceGetCampaignByIdProcedure:
			campaignServiceGetCampaignByIdHandler.ServeHTTP(w, r)
		case CampaignServiceDeleteCampaignByIdProcedure:
			campaignServiceDeleteCampaignByIdHandler.ServeHTTP(w, r)
		case CampaignServiceUpdateCampaignByIdProcedure:
			campaignServiceUpdateCampaignByIdHandler.ServeHTTP(w, r)
		case CampaignServiceGetCampaignSubscriberCommentsProcedure:
			campaignServiceGetCampaignSubscriberCommentsHandler.ServeHTTP(w, r)
		case CampaignServiceGetMySupportedCampaignsProcedure:
			campaignServiceGetMySupportedCampaignsHandler.ServeHTTP(w, r)
		case CampaignServiceGetPopularCampaignProcedure:
			campaignServiceGetPopularCampaignHandler.ServeHTTP(w, r)
		case CampaignServiceGetRelatedCampaignProcedure:
			campaignServiceGetRelatedCampaignHandler.ServeHTTP(w, r)
		case CampaignServiceGetMyCampaignNamesProcedure:
			campaignServiceGetMyCampaignNamesHandler.ServeHTTP(w, r)
		case CampaignServiceSearchCampaignProcedure:
			campaignServiceSearchCampaignHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCampaignServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCampaignServiceHandler struct{}

func (UnimplementedCampaignServiceHandler) AddCampaign(context.Context, *connect.Request[v1.AddCampaignRequest]) (*connect.Response[v1.AddCampaignResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.AddCampaign is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetSubscribers(context.Context, *connect.Request[v1.SubscriberRequest]) (*connect.Response[v1.SubscriberResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetSubscribers is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetAllCampaigns(context.Context, *connect.Request[v1.GetAllCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetAllCampaigns is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetAllActiveCampaigns(context.Context, *connect.Request[v1.GetAllCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetAllActiveCampaigns is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetAllCampaignsByVtuberId(context.Context, *connect.Request[v1.GetAllCampaignsByVtuberRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetAllCampaignsByVtuberId is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetAllActiveCampaignsByVtuberId(context.Context, *connect.Request[v1.GetAllCampaignsByVtuberRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetAllActiveCampaignsByVtuberId is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetMyCampaigns(context.Context, *connect.Request[v1.GetMyCampaignsRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetMyCampaigns is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetCampaignById(context.Context, *connect.Request[v1.GetCampaignByIdRequest]) (*connect.Response[v1.GetCampaignByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetCampaignById is not implemented"))
}

func (UnimplementedCampaignServiceHandler) DeleteCampaignById(context.Context, *connect.Request[v1.DeleteCampaignByIdRequest]) (*connect.Response[v1.DeleteCampaignByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.DeleteCampaignById is not implemented"))
}

func (UnimplementedCampaignServiceHandler) UpdateCampaignById(context.Context, *connect.Request[v1.UpdateCampaignByIdRequest]) (*connect.Response[v1.UpdateCampaignByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.UpdateCampaignById is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetCampaignSubscriberComments(context.Context, *connect.Request[v1.GetCampaignSubscriptionCommentsRequest]) (*connect.Response[v1.GetCampaignSubscriptionCommentsReponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetCampaignSubscriberComments is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetMySupportedCampaigns(context.Context, *connect.Request[v1.GetSupportedCampaignRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetMySupportedCampaigns is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetPopularCampaign(context.Context, *connect.Request[v1.PopularCampaignRequest]) (*connect.Response[v1.Campaign], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetPopularCampaign is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetRelatedCampaign(context.Context, *connect.Request[v1.RelatedCampaignRequest]) (*connect.Response[v1.RelatedCampaignResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetRelatedCampaign is not implemented"))
}

func (UnimplementedCampaignServiceHandler) GetMyCampaignNames(context.Context, *connect.Request[v1.GetMyCampaignsNameRequest]) (*connect.Response[v1.GetMyCampaignNamesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.GetMyCampaignNames is not implemented"))
}

func (UnimplementedCampaignServiceHandler) SearchCampaign(context.Context, *connect.Request[v1.SearchCampaignRequest]) (*connect.Response[v1.GetAllCampaignsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignService.SearchCampaign is not implemented"))
}
