// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: cms/v1/contact.proto

package cmsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ContactRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Name           string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" validate:"required"`
	ProjectName    string                 `protobuf:"bytes,2,opt,name=projectName,proto3" json:"projectName,omitempty" validate:"required"`
	InquiryType    string                 `protobuf:"bytes,3,opt,name=inquiryType,proto3" json:"inquiryType,omitempty" validate:"required"`
	Email          string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty" validate:"required"`
	InquiryDetails string                 `protobuf:"bytes,5,opt,name=inquiryDetails,proto3" json:"inquiryDetails,omitempty" validate:"required"`
	Id             string                 `protobuf:"bytes,6,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ContactRequest) Reset() {
	*x = ContactRequest{}
	mi := &file_cms_v1_contact_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactRequest) ProtoMessage() {}

func (x *ContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_contact_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactRequest.ProtoReflect.Descriptor instead.
func (*ContactRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_contact_proto_rawDescGZIP(), []int{0}
}

func (x *ContactRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ContactRequest) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

func (x *ContactRequest) GetInquiryType() string {
	if x != nil {
		return x.InquiryType
	}
	return ""
}

func (x *ContactRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ContactRequest) GetInquiryDetails() string {
	if x != nil {
		return x.InquiryDetails
	}
	return ""
}

func (x *ContactRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type AdminContactRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	CompanyName    string                 `protobuf:"bytes,1,opt,name=companyName,proto3" json:"companyName,omitempty" validate:"required"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" validate:"required"`
	ProjectUrl     string                 `protobuf:"bytes,3,opt,name=projectUrl,proto3" json:"projectUrl,omitempty" validate:"required"`
	InquiryType    string                 `protobuf:"bytes,4,opt,name=inquiryType,proto3" json:"inquiryType,omitempty" validate:"required"`
	Email          string                 `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty" validate:"required"`
	PhoneNo        *string                `protobuf:"bytes,6,opt,name=phoneNo,proto3,oneof" json:"phoneNo,omitempty"`
	InquiryDetails string                 `protobuf:"bytes,7,opt,name=inquiryDetails,proto3" json:"inquiryDetails,omitempty" validate:"required"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AdminContactRequest) Reset() {
	*x = AdminContactRequest{}
	mi := &file_cms_v1_contact_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminContactRequest) ProtoMessage() {}

func (x *AdminContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_contact_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminContactRequest.ProtoReflect.Descriptor instead.
func (*AdminContactRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_contact_proto_rawDescGZIP(), []int{1}
}

func (x *AdminContactRequest) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

func (x *AdminContactRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AdminContactRequest) GetProjectUrl() string {
	if x != nil {
		return x.ProjectUrl
	}
	return ""
}

func (x *AdminContactRequest) GetInquiryType() string {
	if x != nil {
		return x.InquiryType
	}
	return ""
}

func (x *AdminContactRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AdminContactRequest) GetPhoneNo() string {
	if x != nil && x.PhoneNo != nil {
		return *x.PhoneNo
	}
	return ""
}

func (x *AdminContactRequest) GetInquiryDetails() string {
	if x != nil {
		return x.InquiryDetails
	}
	return ""
}

var File_cms_v1_contact_proto protoreflect.FileDescriptor

const file_cms_v1_contact_proto_rawDesc = "" +
	"\n" +
	"\x14cms/v1/contact.proto\x12\n" +
	"api.cms.v1\x1a\x14authz/v1/authz.proto\x1a\x17shared/v1/generic.proto\"\xb6\x01\n" +
	"\x0eContactRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vprojectName\x18\x02 \x01(\tR\vprojectName\x12 \n" +
	"\vinquiryType\x18\x03 \x01(\tR\vinquiryType\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\x12&\n" +
	"\x0einquiryDetails\x18\x05 \x01(\tR\x0einquiryDetails\x12\x0e\n" +
	"\x02id\x18\x06 \x01(\tR\x02id\"\xf6\x01\n" +
	"\x13AdminContactRequest\x12 \n" +
	"\vcompanyName\x18\x01 \x01(\tR\vcompanyName\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1e\n" +
	"\n" +
	"projectUrl\x18\x03 \x01(\tR\n" +
	"projectUrl\x12 \n" +
	"\vinquiryType\x18\x04 \x01(\tR\vinquiryType\x12\x14\n" +
	"\x05email\x18\x05 \x01(\tR\x05email\x12\x1d\n" +
	"\aphoneNo\x18\x06 \x01(\tH\x00R\aphoneNo\x88\x01\x01\x12&\n" +
	"\x0einquiryDetails\x18\a \x01(\tR\x0einquiryDetailsB\n" +
	"\n" +
	"\b_phoneNo2\xa9\x02\n" +
	"\x0eContactService\x12^\n" +
	"\x18SendCampaignContactEmail\x12\x1a.api.cms.v1.ContactRequest\x1a\x1e.api.shared.v1.GenericResponse\"\x06\x82\xb5\x18\x02\b\x01\x12[\n" +
	"\x15SendEventContactEmail\x12\x1a.api.cms.v1.ContactRequest\x1a\x1e.api.shared.v1.GenericResponse\"\x06\x82\xb5\x18\x02\b\x01\x12Z\n" +
	"\x15SendAdminContactEmail\x12\x1f.api.cms.v1.AdminContactRequest\x1a\x1e.api.shared.v1.GenericResponse\"\x00B,Z*github.com/nsp-inc/vtuber/api/cms/v1;cmsv1b\x06proto3"

var (
	file_cms_v1_contact_proto_rawDescOnce sync.Once
	file_cms_v1_contact_proto_rawDescData []byte
)

func file_cms_v1_contact_proto_rawDescGZIP() []byte {
	file_cms_v1_contact_proto_rawDescOnce.Do(func() {
		file_cms_v1_contact_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cms_v1_contact_proto_rawDesc), len(file_cms_v1_contact_proto_rawDesc)))
	})
	return file_cms_v1_contact_proto_rawDescData
}

var file_cms_v1_contact_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_cms_v1_contact_proto_goTypes = []any{
	(*ContactRequest)(nil),      // 0: api.cms.v1.ContactRequest
	(*AdminContactRequest)(nil), // 1: api.cms.v1.AdminContactRequest
	(*v1.GenericResponse)(nil),  // 2: api.shared.v1.GenericResponse
}
var file_cms_v1_contact_proto_depIdxs = []int32{
	0, // 0: api.cms.v1.ContactService.SendCampaignContactEmail:input_type -> api.cms.v1.ContactRequest
	0, // 1: api.cms.v1.ContactService.SendEventContactEmail:input_type -> api.cms.v1.ContactRequest
	1, // 2: api.cms.v1.ContactService.SendAdminContactEmail:input_type -> api.cms.v1.AdminContactRequest
	2, // 3: api.cms.v1.ContactService.SendCampaignContactEmail:output_type -> api.shared.v1.GenericResponse
	2, // 4: api.cms.v1.ContactService.SendEventContactEmail:output_type -> api.shared.v1.GenericResponse
	2, // 5: api.cms.v1.ContactService.SendAdminContactEmail:output_type -> api.shared.v1.GenericResponse
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_cms_v1_contact_proto_init() }
func file_cms_v1_contact_proto_init() {
	if File_cms_v1_contact_proto != nil {
		return
	}
	file_cms_v1_contact_proto_msgTypes[1].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cms_v1_contact_proto_rawDesc), len(file_cms_v1_contact_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_cms_v1_contact_proto_goTypes,
		DependencyIndexes: file_cms_v1_contact_proto_depIdxs,
		MessageInfos:      file_cms_v1_contact_proto_msgTypes,
	}.Build()
	File_cms_v1_contact_proto = out.File
	file_cms_v1_contact_proto_goTypes = nil
	file_cms_v1_contact_proto_depIdxs = nil
}
