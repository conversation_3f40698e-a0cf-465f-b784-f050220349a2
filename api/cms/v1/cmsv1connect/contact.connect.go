// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: cms/v1/contact.proto

package cmsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/cms/v1"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// ContactServiceName is the fully-qualified name of the ContactService service.
	ContactServiceName = "api.cms.v1.ContactService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// ContactServiceSendCampaignContactEmailProcedure is the fully-qualified name of the
	// ContactService's SendCampaignContactEmail RPC.
	ContactServiceSendCampaignContactEmailProcedure = "/api.cms.v1.ContactService/SendCampaignContactEmail"
	// ContactServiceSendEventContactEmailProcedure is the fully-qualified name of the ContactService's
	// SendEventContactEmail RPC.
	ContactServiceSendEventContactEmailProcedure = "/api.cms.v1.ContactService/SendEventContactEmail"
	// ContactServiceSendAdminContactEmailProcedure is the fully-qualified name of the ContactService's
	// SendAdminContactEmail RPC.
	ContactServiceSendAdminContactEmailProcedure = "/api.cms.v1.ContactService/SendAdminContactEmail"
)

// ContactServiceClient is a client for the api.cms.v1.ContactService service.
type ContactServiceClient interface {
	SendCampaignContactEmail(context.Context, *connect.Request[v1.ContactRequest]) (*connect.Response[v11.GenericResponse], error)
	SendEventContactEmail(context.Context, *connect.Request[v1.ContactRequest]) (*connect.Response[v11.GenericResponse], error)
	SendAdminContactEmail(context.Context, *connect.Request[v1.AdminContactRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewContactServiceClient constructs a client for the api.cms.v1.ContactService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewContactServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) ContactServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	contactServiceMethods := v1.File_cms_v1_contact_proto.Services().ByName("ContactService").Methods()
	return &contactServiceClient{
		sendCampaignContactEmail: connect.NewClient[v1.ContactRequest, v11.GenericResponse](
			httpClient,
			baseURL+ContactServiceSendCampaignContactEmailProcedure,
			connect.WithSchema(contactServiceMethods.ByName("SendCampaignContactEmail")),
			connect.WithClientOptions(opts...),
		),
		sendEventContactEmail: connect.NewClient[v1.ContactRequest, v11.GenericResponse](
			httpClient,
			baseURL+ContactServiceSendEventContactEmailProcedure,
			connect.WithSchema(contactServiceMethods.ByName("SendEventContactEmail")),
			connect.WithClientOptions(opts...),
		),
		sendAdminContactEmail: connect.NewClient[v1.AdminContactRequest, v11.GenericResponse](
			httpClient,
			baseURL+ContactServiceSendAdminContactEmailProcedure,
			connect.WithSchema(contactServiceMethods.ByName("SendAdminContactEmail")),
			connect.WithClientOptions(opts...),
		),
	}
}

// contactServiceClient implements ContactServiceClient.
type contactServiceClient struct {
	sendCampaignContactEmail *connect.Client[v1.ContactRequest, v11.GenericResponse]
	sendEventContactEmail    *connect.Client[v1.ContactRequest, v11.GenericResponse]
	sendAdminContactEmail    *connect.Client[v1.AdminContactRequest, v11.GenericResponse]
}

// SendCampaignContactEmail calls api.cms.v1.ContactService.SendCampaignContactEmail.
func (c *contactServiceClient) SendCampaignContactEmail(ctx context.Context, req *connect.Request[v1.ContactRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.sendCampaignContactEmail.CallUnary(ctx, req)
}

// SendEventContactEmail calls api.cms.v1.ContactService.SendEventContactEmail.
func (c *contactServiceClient) SendEventContactEmail(ctx context.Context, req *connect.Request[v1.ContactRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.sendEventContactEmail.CallUnary(ctx, req)
}

// SendAdminContactEmail calls api.cms.v1.ContactService.SendAdminContactEmail.
func (c *contactServiceClient) SendAdminContactEmail(ctx context.Context, req *connect.Request[v1.AdminContactRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.sendAdminContactEmail.CallUnary(ctx, req)
}

// ContactServiceHandler is an implementation of the api.cms.v1.ContactService service.
type ContactServiceHandler interface {
	SendCampaignContactEmail(context.Context, *connect.Request[v1.ContactRequest]) (*connect.Response[v11.GenericResponse], error)
	SendEventContactEmail(context.Context, *connect.Request[v1.ContactRequest]) (*connect.Response[v11.GenericResponse], error)
	SendAdminContactEmail(context.Context, *connect.Request[v1.AdminContactRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewContactServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewContactServiceHandler(svc ContactServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	contactServiceMethods := v1.File_cms_v1_contact_proto.Services().ByName("ContactService").Methods()
	contactServiceSendCampaignContactEmailHandler := connect.NewUnaryHandler(
		ContactServiceSendCampaignContactEmailProcedure,
		svc.SendCampaignContactEmail,
		connect.WithSchema(contactServiceMethods.ByName("SendCampaignContactEmail")),
		connect.WithHandlerOptions(opts...),
	)
	contactServiceSendEventContactEmailHandler := connect.NewUnaryHandler(
		ContactServiceSendEventContactEmailProcedure,
		svc.SendEventContactEmail,
		connect.WithSchema(contactServiceMethods.ByName("SendEventContactEmail")),
		connect.WithHandlerOptions(opts...),
	)
	contactServiceSendAdminContactEmailHandler := connect.NewUnaryHandler(
		ContactServiceSendAdminContactEmailProcedure,
		svc.SendAdminContactEmail,
		connect.WithSchema(contactServiceMethods.ByName("SendAdminContactEmail")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.cms.v1.ContactService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case ContactServiceSendCampaignContactEmailProcedure:
			contactServiceSendCampaignContactEmailHandler.ServeHTTP(w, r)
		case ContactServiceSendEventContactEmailProcedure:
			contactServiceSendEventContactEmailHandler.ServeHTTP(w, r)
		case ContactServiceSendAdminContactEmailProcedure:
			contactServiceSendAdminContactEmailHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedContactServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedContactServiceHandler struct{}

func (UnimplementedContactServiceHandler) SendCampaignContactEmail(context.Context, *connect.Request[v1.ContactRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.ContactService.SendCampaignContactEmail is not implemented"))
}

func (UnimplementedContactServiceHandler) SendEventContactEmail(context.Context, *connect.Request[v1.ContactRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.ContactService.SendEventContactEmail is not implemented"))
}

func (UnimplementedContactServiceHandler) SendAdminContactEmail(context.Context, *connect.Request[v1.AdminContactRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.ContactService.SendAdminContactEmail is not implemented"))
}
