// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: vtubers/v1/vtuberprofiles.proto

package vtubersv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// VtuberProfilesServiceName is the fully-qualified name of the VtuberProfilesService service.
	VtuberProfilesServiceName = "api.vtubers.v1.VtuberProfilesService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// VtuberProfilesServiceAddVtuberProfileProcedure is the fully-qualified name of the
	// VtuberProfilesService's AddVtuberProfile RPC.
	VtuberProfilesServiceAddVtuberProfileProcedure = "/api.vtubers.v1.VtuberProfilesService/AddVtuberProfile"
	// VtuberProfilesServiceGetVtuberProfileByIdProcedure is the fully-qualified name of the
	// VtuberProfilesService's GetVtuberProfileById RPC.
	VtuberProfilesServiceGetVtuberProfileByIdProcedure = "/api.vtubers.v1.VtuberProfilesService/GetVtuberProfileById"
	// VtuberProfilesServiceGetVtuberProfileProcedure is the fully-qualified name of the
	// VtuberProfilesService's GetVtuberProfile RPC.
	VtuberProfilesServiceGetVtuberProfileProcedure = "/api.vtubers.v1.VtuberProfilesService/GetVtuberProfile"
	// VtuberProfilesServiceUpdateVtuberProfileProcedure is the fully-qualified name of the
	// VtuberProfilesService's UpdateVtuberProfile RPC.
	VtuberProfilesServiceUpdateVtuberProfileProcedure = "/api.vtubers.v1.VtuberProfilesService/UpdateVtuberProfile"
	// VtuberProfilesServiceGetAllVtuberProfilesProcedure is the fully-qualified name of the
	// VtuberProfilesService's GetAllVtuberProfiles RPC.
	VtuberProfilesServiceGetAllVtuberProfilesProcedure = "/api.vtubers.v1.VtuberProfilesService/GetAllVtuberProfiles"
	// VtuberProfilesServiceVerifyVtuberProfileProcedure is the fully-qualified name of the
	// VtuberProfilesService's VerifyVtuberProfile RPC.
	VtuberProfilesServiceVerifyVtuberProfileProcedure = "/api.vtubers.v1.VtuberProfilesService/VerifyVtuberProfile"
	// VtuberProfilesServiceApplyVtuberAccessProcedure is the fully-qualified name of the
	// VtuberProfilesService's ApplyVtuberAccess RPC.
	VtuberProfilesServiceApplyVtuberAccessProcedure = "/api.vtubers.v1.VtuberProfilesService/ApplyVtuberAccess"
	// VtuberProfilesServiceGetMyVtuberAccessRequestsProcedure is the fully-qualified name of the
	// VtuberProfilesService's GetMyVtuberAccessRequests RPC.
	VtuberProfilesServiceGetMyVtuberAccessRequestsProcedure = "/api.vtubers.v1.VtuberProfilesService/GetMyVtuberAccessRequests"
	// VtuberProfilesServiceGetAllVtuberProfileAccessProcedure is the fully-qualified name of the
	// VtuberProfilesService's GetAllVtuberProfileAccess RPC.
	VtuberProfilesServiceGetAllVtuberProfileAccessProcedure = "/api.vtubers.v1.VtuberProfilesService/GetAllVtuberProfileAccess"
	// VtuberProfilesServiceDenyVtuberProfileProcedure is the fully-qualified name of the
	// VtuberProfilesService's DenyVtuberProfile RPC.
	VtuberProfilesServiceDenyVtuberProfileProcedure = "/api.vtubers.v1.VtuberProfilesService/DenyVtuberProfile"
	// VtuberProfilesServiceUpdateVtuberAccessRequestProcedure is the fully-qualified name of the
	// VtuberProfilesService's UpdateVtuberAccessRequest RPC.
	VtuberProfilesServiceUpdateVtuberAccessRequestProcedure = "/api.vtubers.v1.VtuberProfilesService/UpdateVtuberAccessRequest"
	// VtuberProfilesServiceSearchVtuberProfilesProcedure is the fully-qualified name of the
	// VtuberProfilesService's SearchVtuberProfiles RPC.
	VtuberProfilesServiceSearchVtuberProfilesProcedure = "/api.vtubers.v1.VtuberProfilesService/SearchVtuberProfiles"
)

// VtuberProfilesServiceClient is a client for the api.vtubers.v1.VtuberProfilesService service.
type VtuberProfilesServiceClient interface {
	AddVtuberProfile(context.Context, *connect.Request[v1.AddVtuberProfileRequest]) (*connect.Response[v1.AddVtuberProfileResponse], error)
	GetVtuberProfileById(context.Context, *connect.Request[v1.GetVtuberProfileByIdRequest]) (*connect.Response[v1.GetVtuberProfileByIdResponse], error)
	GetVtuberProfile(context.Context, *connect.Request[v1.GetVtuberProfileRequest]) (*connect.Response[v1.GetVtuberProfileByIdResponse], error)
	UpdateVtuberProfile(context.Context, *connect.Request[v1.UpdateVtuberProfileRequest]) (*connect.Response[v1.UpdateVtuberProfileResponse], error)
	GetAllVtuberProfiles(context.Context, *connect.Request[v1.GetAllVtuberProfilesRequest]) (*connect.Response[v1.GetAllVtuberProfilesResponse], error)
	//	rpc DeleteVtuberProfileById(DeleteVtuberProfileByIdRequest) returns (api.shared.v1.GenericResponse) {
	//	  option (api.authz.v1.options) = {
	//	    require: true
	//	  };
	//	}
	VerifyVtuberProfile(context.Context, *connect.Request[v1.VerifyVtuberProfileRequest]) (*connect.Response[v1.VerifyVtuberProfileResponse], error)
	ApplyVtuberAccess(context.Context, *connect.Request[v1.CreateVtuberProfileAccessRequest]) (*connect.Response[v1.CreateVtuberProfileAccessResponse], error)
	GetMyVtuberAccessRequests(context.Context, *connect.Request[v1.GetAllVtuberProfileAccessRequest]) (*connect.Response[v1.VtuberAccessRequest], error)
	GetAllVtuberProfileAccess(context.Context, *connect.Request[v1.GetAllVtuberProfileAccessRequest]) (*connect.Response[v1.GetAllVtuberProfileAccessResponse], error)
	DenyVtuberProfile(context.Context, *connect.Request[v1.DenyVtuberProfileRequest]) (*connect.Response[v1.DenyVtuberProfileResponse], error)
	UpdateVtuberAccessRequest(context.Context, *connect.Request[v1.UpdateVtuberProfileAccessRequest]) (*connect.Response[v1.UpdateVtuberProfileAccessResponse], error)
	SearchVtuberProfiles(context.Context, *connect.Request[v1.SearchVtuberProfilesRequest]) (*connect.Response[v1.GetAllVtuberProfilesResponse], error)
}

// NewVtuberProfilesServiceClient constructs a client for the api.vtubers.v1.VtuberProfilesService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewVtuberProfilesServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) VtuberProfilesServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	vtuberProfilesServiceMethods := v1.File_vtubers_v1_vtuberprofiles_proto.Services().ByName("VtuberProfilesService").Methods()
	return &vtuberProfilesServiceClient{
		addVtuberProfile: connect.NewClient[v1.AddVtuberProfileRequest, v1.AddVtuberProfileResponse](
			httpClient,
			baseURL+VtuberProfilesServiceAddVtuberProfileProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("AddVtuberProfile")),
			connect.WithClientOptions(opts...),
		),
		getVtuberProfileById: connect.NewClient[v1.GetVtuberProfileByIdRequest, v1.GetVtuberProfileByIdResponse](
			httpClient,
			baseURL+VtuberProfilesServiceGetVtuberProfileByIdProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("GetVtuberProfileById")),
			connect.WithClientOptions(opts...),
		),
		getVtuberProfile: connect.NewClient[v1.GetVtuberProfileRequest, v1.GetVtuberProfileByIdResponse](
			httpClient,
			baseURL+VtuberProfilesServiceGetVtuberProfileProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("GetVtuberProfile")),
			connect.WithClientOptions(opts...),
		),
		updateVtuberProfile: connect.NewClient[v1.UpdateVtuberProfileRequest, v1.UpdateVtuberProfileResponse](
			httpClient,
			baseURL+VtuberProfilesServiceUpdateVtuberProfileProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("UpdateVtuberProfile")),
			connect.WithClientOptions(opts...),
		),
		getAllVtuberProfiles: connect.NewClient[v1.GetAllVtuberProfilesRequest, v1.GetAllVtuberProfilesResponse](
			httpClient,
			baseURL+VtuberProfilesServiceGetAllVtuberProfilesProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("GetAllVtuberProfiles")),
			connect.WithClientOptions(opts...),
		),
		verifyVtuberProfile: connect.NewClient[v1.VerifyVtuberProfileRequest, v1.VerifyVtuberProfileResponse](
			httpClient,
			baseURL+VtuberProfilesServiceVerifyVtuberProfileProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("VerifyVtuberProfile")),
			connect.WithClientOptions(opts...),
		),
		applyVtuberAccess: connect.NewClient[v1.CreateVtuberProfileAccessRequest, v1.CreateVtuberProfileAccessResponse](
			httpClient,
			baseURL+VtuberProfilesServiceApplyVtuberAccessProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("ApplyVtuberAccess")),
			connect.WithClientOptions(opts...),
		),
		getMyVtuberAccessRequests: connect.NewClient[v1.GetAllVtuberProfileAccessRequest, v1.VtuberAccessRequest](
			httpClient,
			baseURL+VtuberProfilesServiceGetMyVtuberAccessRequestsProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("GetMyVtuberAccessRequests")),
			connect.WithClientOptions(opts...),
		),
		getAllVtuberProfileAccess: connect.NewClient[v1.GetAllVtuberProfileAccessRequest, v1.GetAllVtuberProfileAccessResponse](
			httpClient,
			baseURL+VtuberProfilesServiceGetAllVtuberProfileAccessProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("GetAllVtuberProfileAccess")),
			connect.WithClientOptions(opts...),
		),
		denyVtuberProfile: connect.NewClient[v1.DenyVtuberProfileRequest, v1.DenyVtuberProfileResponse](
			httpClient,
			baseURL+VtuberProfilesServiceDenyVtuberProfileProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("DenyVtuberProfile")),
			connect.WithClientOptions(opts...),
		),
		updateVtuberAccessRequest: connect.NewClient[v1.UpdateVtuberProfileAccessRequest, v1.UpdateVtuberProfileAccessResponse](
			httpClient,
			baseURL+VtuberProfilesServiceUpdateVtuberAccessRequestProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("UpdateVtuberAccessRequest")),
			connect.WithClientOptions(opts...),
		),
		searchVtuberProfiles: connect.NewClient[v1.SearchVtuberProfilesRequest, v1.GetAllVtuberProfilesResponse](
			httpClient,
			baseURL+VtuberProfilesServiceSearchVtuberProfilesProcedure,
			connect.WithSchema(vtuberProfilesServiceMethods.ByName("SearchVtuberProfiles")),
			connect.WithClientOptions(opts...),
		),
	}
}

// vtuberProfilesServiceClient implements VtuberProfilesServiceClient.
type vtuberProfilesServiceClient struct {
	addVtuberProfile          *connect.Client[v1.AddVtuberProfileRequest, v1.AddVtuberProfileResponse]
	getVtuberProfileById      *connect.Client[v1.GetVtuberProfileByIdRequest, v1.GetVtuberProfileByIdResponse]
	getVtuberProfile          *connect.Client[v1.GetVtuberProfileRequest, v1.GetVtuberProfileByIdResponse]
	updateVtuberProfile       *connect.Client[v1.UpdateVtuberProfileRequest, v1.UpdateVtuberProfileResponse]
	getAllVtuberProfiles      *connect.Client[v1.GetAllVtuberProfilesRequest, v1.GetAllVtuberProfilesResponse]
	verifyVtuberProfile       *connect.Client[v1.VerifyVtuberProfileRequest, v1.VerifyVtuberProfileResponse]
	applyVtuberAccess         *connect.Client[v1.CreateVtuberProfileAccessRequest, v1.CreateVtuberProfileAccessResponse]
	getMyVtuberAccessRequests *connect.Client[v1.GetAllVtuberProfileAccessRequest, v1.VtuberAccessRequest]
	getAllVtuberProfileAccess *connect.Client[v1.GetAllVtuberProfileAccessRequest, v1.GetAllVtuberProfileAccessResponse]
	denyVtuberProfile         *connect.Client[v1.DenyVtuberProfileRequest, v1.DenyVtuberProfileResponse]
	updateVtuberAccessRequest *connect.Client[v1.UpdateVtuberProfileAccessRequest, v1.UpdateVtuberProfileAccessResponse]
	searchVtuberProfiles      *connect.Client[v1.SearchVtuberProfilesRequest, v1.GetAllVtuberProfilesResponse]
}

// AddVtuberProfile calls api.vtubers.v1.VtuberProfilesService.AddVtuberProfile.
func (c *vtuberProfilesServiceClient) AddVtuberProfile(ctx context.Context, req *connect.Request[v1.AddVtuberProfileRequest]) (*connect.Response[v1.AddVtuberProfileResponse], error) {
	return c.addVtuberProfile.CallUnary(ctx, req)
}

// GetVtuberProfileById calls api.vtubers.v1.VtuberProfilesService.GetVtuberProfileById.
func (c *vtuberProfilesServiceClient) GetVtuberProfileById(ctx context.Context, req *connect.Request[v1.GetVtuberProfileByIdRequest]) (*connect.Response[v1.GetVtuberProfileByIdResponse], error) {
	return c.getVtuberProfileById.CallUnary(ctx, req)
}

// GetVtuberProfile calls api.vtubers.v1.VtuberProfilesService.GetVtuberProfile.
func (c *vtuberProfilesServiceClient) GetVtuberProfile(ctx context.Context, req *connect.Request[v1.GetVtuberProfileRequest]) (*connect.Response[v1.GetVtuberProfileByIdResponse], error) {
	return c.getVtuberProfile.CallUnary(ctx, req)
}

// UpdateVtuberProfile calls api.vtubers.v1.VtuberProfilesService.UpdateVtuberProfile.
func (c *vtuberProfilesServiceClient) UpdateVtuberProfile(ctx context.Context, req *connect.Request[v1.UpdateVtuberProfileRequest]) (*connect.Response[v1.UpdateVtuberProfileResponse], error) {
	return c.updateVtuberProfile.CallUnary(ctx, req)
}

// GetAllVtuberProfiles calls api.vtubers.v1.VtuberProfilesService.GetAllVtuberProfiles.
func (c *vtuberProfilesServiceClient) GetAllVtuberProfiles(ctx context.Context, req *connect.Request[v1.GetAllVtuberProfilesRequest]) (*connect.Response[v1.GetAllVtuberProfilesResponse], error) {
	return c.getAllVtuberProfiles.CallUnary(ctx, req)
}

// VerifyVtuberProfile calls api.vtubers.v1.VtuberProfilesService.VerifyVtuberProfile.
func (c *vtuberProfilesServiceClient) VerifyVtuberProfile(ctx context.Context, req *connect.Request[v1.VerifyVtuberProfileRequest]) (*connect.Response[v1.VerifyVtuberProfileResponse], error) {
	return c.verifyVtuberProfile.CallUnary(ctx, req)
}

// ApplyVtuberAccess calls api.vtubers.v1.VtuberProfilesService.ApplyVtuberAccess.
func (c *vtuberProfilesServiceClient) ApplyVtuberAccess(ctx context.Context, req *connect.Request[v1.CreateVtuberProfileAccessRequest]) (*connect.Response[v1.CreateVtuberProfileAccessResponse], error) {
	return c.applyVtuberAccess.CallUnary(ctx, req)
}

// GetMyVtuberAccessRequests calls api.vtubers.v1.VtuberProfilesService.GetMyVtuberAccessRequests.
func (c *vtuberProfilesServiceClient) GetMyVtuberAccessRequests(ctx context.Context, req *connect.Request[v1.GetAllVtuberProfileAccessRequest]) (*connect.Response[v1.VtuberAccessRequest], error) {
	return c.getMyVtuberAccessRequests.CallUnary(ctx, req)
}

// GetAllVtuberProfileAccess calls api.vtubers.v1.VtuberProfilesService.GetAllVtuberProfileAccess.
func (c *vtuberProfilesServiceClient) GetAllVtuberProfileAccess(ctx context.Context, req *connect.Request[v1.GetAllVtuberProfileAccessRequest]) (*connect.Response[v1.GetAllVtuberProfileAccessResponse], error) {
	return c.getAllVtuberProfileAccess.CallUnary(ctx, req)
}

// DenyVtuberProfile calls api.vtubers.v1.VtuberProfilesService.DenyVtuberProfile.
func (c *vtuberProfilesServiceClient) DenyVtuberProfile(ctx context.Context, req *connect.Request[v1.DenyVtuberProfileRequest]) (*connect.Response[v1.DenyVtuberProfileResponse], error) {
	return c.denyVtuberProfile.CallUnary(ctx, req)
}

// UpdateVtuberAccessRequest calls api.vtubers.v1.VtuberProfilesService.UpdateVtuberAccessRequest.
func (c *vtuberProfilesServiceClient) UpdateVtuberAccessRequest(ctx context.Context, req *connect.Request[v1.UpdateVtuberProfileAccessRequest]) (*connect.Response[v1.UpdateVtuberProfileAccessResponse], error) {
	return c.updateVtuberAccessRequest.CallUnary(ctx, req)
}

// SearchVtuberProfiles calls api.vtubers.v1.VtuberProfilesService.SearchVtuberProfiles.
func (c *vtuberProfilesServiceClient) SearchVtuberProfiles(ctx context.Context, req *connect.Request[v1.SearchVtuberProfilesRequest]) (*connect.Response[v1.GetAllVtuberProfilesResponse], error) {
	return c.searchVtuberProfiles.CallUnary(ctx, req)
}

// VtuberProfilesServiceHandler is an implementation of the api.vtubers.v1.VtuberProfilesService
// service.
type VtuberProfilesServiceHandler interface {
	AddVtuberProfile(context.Context, *connect.Request[v1.AddVtuberProfileRequest]) (*connect.Response[v1.AddVtuberProfileResponse], error)
	GetVtuberProfileById(context.Context, *connect.Request[v1.GetVtuberProfileByIdRequest]) (*connect.Response[v1.GetVtuberProfileByIdResponse], error)
	GetVtuberProfile(context.Context, *connect.Request[v1.GetVtuberProfileRequest]) (*connect.Response[v1.GetVtuberProfileByIdResponse], error)
	UpdateVtuberProfile(context.Context, *connect.Request[v1.UpdateVtuberProfileRequest]) (*connect.Response[v1.UpdateVtuberProfileResponse], error)
	GetAllVtuberProfiles(context.Context, *connect.Request[v1.GetAllVtuberProfilesRequest]) (*connect.Response[v1.GetAllVtuberProfilesResponse], error)
	//	rpc DeleteVtuberProfileById(DeleteVtuberProfileByIdRequest) returns (api.shared.v1.GenericResponse) {
	//	  option (api.authz.v1.options) = {
	//	    require: true
	//	  };
	//	}
	VerifyVtuberProfile(context.Context, *connect.Request[v1.VerifyVtuberProfileRequest]) (*connect.Response[v1.VerifyVtuberProfileResponse], error)
	ApplyVtuberAccess(context.Context, *connect.Request[v1.CreateVtuberProfileAccessRequest]) (*connect.Response[v1.CreateVtuberProfileAccessResponse], error)
	GetMyVtuberAccessRequests(context.Context, *connect.Request[v1.GetAllVtuberProfileAccessRequest]) (*connect.Response[v1.VtuberAccessRequest], error)
	GetAllVtuberProfileAccess(context.Context, *connect.Request[v1.GetAllVtuberProfileAccessRequest]) (*connect.Response[v1.GetAllVtuberProfileAccessResponse], error)
	DenyVtuberProfile(context.Context, *connect.Request[v1.DenyVtuberProfileRequest]) (*connect.Response[v1.DenyVtuberProfileResponse], error)
	UpdateVtuberAccessRequest(context.Context, *connect.Request[v1.UpdateVtuberProfileAccessRequest]) (*connect.Response[v1.UpdateVtuberProfileAccessResponse], error)
	SearchVtuberProfiles(context.Context, *connect.Request[v1.SearchVtuberProfilesRequest]) (*connect.Response[v1.GetAllVtuberProfilesResponse], error)
}

// NewVtuberProfilesServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewVtuberProfilesServiceHandler(svc VtuberProfilesServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	vtuberProfilesServiceMethods := v1.File_vtubers_v1_vtuberprofiles_proto.Services().ByName("VtuberProfilesService").Methods()
	vtuberProfilesServiceAddVtuberProfileHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceAddVtuberProfileProcedure,
		svc.AddVtuberProfile,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("AddVtuberProfile")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberProfilesServiceGetVtuberProfileByIdHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceGetVtuberProfileByIdProcedure,
		svc.GetVtuberProfileById,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("GetVtuberProfileById")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberProfilesServiceGetVtuberProfileHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceGetVtuberProfileProcedure,
		svc.GetVtuberProfile,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("GetVtuberProfile")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberProfilesServiceUpdateVtuberProfileHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceUpdateVtuberProfileProcedure,
		svc.UpdateVtuberProfile,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("UpdateVtuberProfile")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberProfilesServiceGetAllVtuberProfilesHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceGetAllVtuberProfilesProcedure,
		svc.GetAllVtuberProfiles,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("GetAllVtuberProfiles")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberProfilesServiceVerifyVtuberProfileHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceVerifyVtuberProfileProcedure,
		svc.VerifyVtuberProfile,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("VerifyVtuberProfile")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberProfilesServiceApplyVtuberAccessHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceApplyVtuberAccessProcedure,
		svc.ApplyVtuberAccess,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("ApplyVtuberAccess")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberProfilesServiceGetMyVtuberAccessRequestsHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceGetMyVtuberAccessRequestsProcedure,
		svc.GetMyVtuberAccessRequests,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("GetMyVtuberAccessRequests")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberProfilesServiceGetAllVtuberProfileAccessHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceGetAllVtuberProfileAccessProcedure,
		svc.GetAllVtuberProfileAccess,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("GetAllVtuberProfileAccess")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberProfilesServiceDenyVtuberProfileHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceDenyVtuberProfileProcedure,
		svc.DenyVtuberProfile,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("DenyVtuberProfile")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberProfilesServiceUpdateVtuberAccessRequestHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceUpdateVtuberAccessRequestProcedure,
		svc.UpdateVtuberAccessRequest,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("UpdateVtuberAccessRequest")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberProfilesServiceSearchVtuberProfilesHandler := connect.NewUnaryHandler(
		VtuberProfilesServiceSearchVtuberProfilesProcedure,
		svc.SearchVtuberProfiles,
		connect.WithSchema(vtuberProfilesServiceMethods.ByName("SearchVtuberProfiles")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.vtubers.v1.VtuberProfilesService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case VtuberProfilesServiceAddVtuberProfileProcedure:
			vtuberProfilesServiceAddVtuberProfileHandler.ServeHTTP(w, r)
		case VtuberProfilesServiceGetVtuberProfileByIdProcedure:
			vtuberProfilesServiceGetVtuberProfileByIdHandler.ServeHTTP(w, r)
		case VtuberProfilesServiceGetVtuberProfileProcedure:
			vtuberProfilesServiceGetVtuberProfileHandler.ServeHTTP(w, r)
		case VtuberProfilesServiceUpdateVtuberProfileProcedure:
			vtuberProfilesServiceUpdateVtuberProfileHandler.ServeHTTP(w, r)
		case VtuberProfilesServiceGetAllVtuberProfilesProcedure:
			vtuberProfilesServiceGetAllVtuberProfilesHandler.ServeHTTP(w, r)
		case VtuberProfilesServiceVerifyVtuberProfileProcedure:
			vtuberProfilesServiceVerifyVtuberProfileHandler.ServeHTTP(w, r)
		case VtuberProfilesServiceApplyVtuberAccessProcedure:
			vtuberProfilesServiceApplyVtuberAccessHandler.ServeHTTP(w, r)
		case VtuberProfilesServiceGetMyVtuberAccessRequestsProcedure:
			vtuberProfilesServiceGetMyVtuberAccessRequestsHandler.ServeHTTP(w, r)
		case VtuberProfilesServiceGetAllVtuberProfileAccessProcedure:
			vtuberProfilesServiceGetAllVtuberProfileAccessHandler.ServeHTTP(w, r)
		case VtuberProfilesServiceDenyVtuberProfileProcedure:
			vtuberProfilesServiceDenyVtuberProfileHandler.ServeHTTP(w, r)
		case VtuberProfilesServiceUpdateVtuberAccessRequestProcedure:
			vtuberProfilesServiceUpdateVtuberAccessRequestHandler.ServeHTTP(w, r)
		case VtuberProfilesServiceSearchVtuberProfilesProcedure:
			vtuberProfilesServiceSearchVtuberProfilesHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedVtuberProfilesServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedVtuberProfilesServiceHandler struct{}

func (UnimplementedVtuberProfilesServiceHandler) AddVtuberProfile(context.Context, *connect.Request[v1.AddVtuberProfileRequest]) (*connect.Response[v1.AddVtuberProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.AddVtuberProfile is not implemented"))
}

func (UnimplementedVtuberProfilesServiceHandler) GetVtuberProfileById(context.Context, *connect.Request[v1.GetVtuberProfileByIdRequest]) (*connect.Response[v1.GetVtuberProfileByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.GetVtuberProfileById is not implemented"))
}

func (UnimplementedVtuberProfilesServiceHandler) GetVtuberProfile(context.Context, *connect.Request[v1.GetVtuberProfileRequest]) (*connect.Response[v1.GetVtuberProfileByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.GetVtuberProfile is not implemented"))
}

func (UnimplementedVtuberProfilesServiceHandler) UpdateVtuberProfile(context.Context, *connect.Request[v1.UpdateVtuberProfileRequest]) (*connect.Response[v1.UpdateVtuberProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.UpdateVtuberProfile is not implemented"))
}

func (UnimplementedVtuberProfilesServiceHandler) GetAllVtuberProfiles(context.Context, *connect.Request[v1.GetAllVtuberProfilesRequest]) (*connect.Response[v1.GetAllVtuberProfilesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.GetAllVtuberProfiles is not implemented"))
}

func (UnimplementedVtuberProfilesServiceHandler) VerifyVtuberProfile(context.Context, *connect.Request[v1.VerifyVtuberProfileRequest]) (*connect.Response[v1.VerifyVtuberProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.VerifyVtuberProfile is not implemented"))
}

func (UnimplementedVtuberProfilesServiceHandler) ApplyVtuberAccess(context.Context, *connect.Request[v1.CreateVtuberProfileAccessRequest]) (*connect.Response[v1.CreateVtuberProfileAccessResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.ApplyVtuberAccess is not implemented"))
}

func (UnimplementedVtuberProfilesServiceHandler) GetMyVtuberAccessRequests(context.Context, *connect.Request[v1.GetAllVtuberProfileAccessRequest]) (*connect.Response[v1.VtuberAccessRequest], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.GetMyVtuberAccessRequests is not implemented"))
}

func (UnimplementedVtuberProfilesServiceHandler) GetAllVtuberProfileAccess(context.Context, *connect.Request[v1.GetAllVtuberProfileAccessRequest]) (*connect.Response[v1.GetAllVtuberProfileAccessResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.GetAllVtuberProfileAccess is not implemented"))
}

func (UnimplementedVtuberProfilesServiceHandler) DenyVtuberProfile(context.Context, *connect.Request[v1.DenyVtuberProfileRequest]) (*connect.Response[v1.DenyVtuberProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.DenyVtuberProfile is not implemented"))
}

func (UnimplementedVtuberProfilesServiceHandler) UpdateVtuberAccessRequest(context.Context, *connect.Request[v1.UpdateVtuberProfileAccessRequest]) (*connect.Response[v1.UpdateVtuberProfileAccessResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.UpdateVtuberAccessRequest is not implemented"))
}

func (UnimplementedVtuberProfilesServiceHandler) SearchVtuberProfiles(context.Context, *connect.Request[v1.SearchVtuberProfilesRequest]) (*connect.Response[v1.GetAllVtuberProfilesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberProfilesService.SearchVtuberProfiles is not implemented"))
}
