package cms

import (
	"context"
	"errors"

	cmsv1 "github.com/nsp-inc/vtuber/api/cms/v1"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	campaignsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaigns"
	eventsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/events"
	usersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/users"

	"connectrpc.com/connect"
	"github.com/nsp-inc/vtuber/packages/mail"
	"github.com/nsp-inc/vtuber/packages/web"
)

type ContactService struct {
	userRepo     *usersqueries.Queries
	campaignRepo *campaignsqueries.Queries
	eventRepo    *eventsqueries.Queries
}

func NewContactService(userRepo *usersqueries.Queries, campaignRepo *campaignsqueries.Queries, eventRepo *eventsqueries.Queries) *ContactService {
	return &ContactService{
		userRepo:     userRepo,
		campaignRepo: campaignRepo,
		eventRepo:    eventRepo,
	}
}

func (c ContactService) SendCampaignContactEmail(ctx context.Context, req *connect.Request[cmsv1.ContactRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	campaign, err := c.campaignRepo.GetOneCampaign(ctx, req.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "campaign", nil),
		}))
	}
	user, err := c.userRepo.GetUserByVtuberId(ctx, campaign.VtuberID)
	if err != nil {
		return nil, err
	}
	if user.Email == nil {
		return nil, errors.New(web.GetTranslation(ctx, "vtuberEmailNotFound", nil))
	}
	mail.ContentContactEmail(ctx,
		campaign.Slug,
		true,
		campaign.Name,
		req.Msg.Name,
		req.Msg.Email,
		req.Msg.ProjectName,
		req.Msg.InquiryType,
		req.Msg.InquiryDetails,
		*user.Email,
	)
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  201,
		Message: web.GetTranslation(ctx, "campaginContactEmailSuccess", nil),
		Success: true,
	}), nil
}

func (c ContactService) SendEventContactEmail(ctx context.Context, req *connect.Request[cmsv1.ContactRequest]) (*connect.Response[sharedv1.GenericResponse], error) {

	event, err := c.eventRepo.GetOneEvent(ctx, req.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "event", nil),
		}))
	}

	user, err := c.userRepo.GetUserByID(ctx, event.UserID)
	if err != nil {
		return nil, err
	}
	if user.Email == nil {
		return nil, errors.New(web.GetTranslation(ctx, "vtuberEmailNotFound", nil))
	}
	mail.ContentContactEmail(ctx,
		event.Slug,
		false,
		event.Title,
		req.Msg.Name,
		req.Msg.Email,
		req.Msg.ProjectName,
		req.Msg.InquiryType,
		req.Msg.InquiryDetails,
		*user.Email,
	)
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  201,
		Message: web.GetTranslation(ctx, "eventContactEmailSuccess", nil),
		Success: true,
	}), nil
}

func (c ContactService) SendAdminContactEmail(ctx context.Context, req *connect.Request[cmsv1.AdminContactRequest]) (*connect.Response[sharedv1.GenericResponse], error) {

	mail.AdminContactEmail(ctx,
		req.Msg.PhoneNo,
		req.Msg.CompanyName,
		req.Msg.Name,
		req.Msg.Email,
		req.Msg.ProjectUrl,
		req.Msg.InquiryType,
		req.Msg.InquiryDetails,
	)
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  201,
		Message: web.GetTranslation(ctx, "adminEmailSuccess", nil),
		Success: true,
	}), nil
}
